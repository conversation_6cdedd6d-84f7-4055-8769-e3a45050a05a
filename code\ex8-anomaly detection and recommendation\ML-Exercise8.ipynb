{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 机器学习练习 8 - 异常检测和推荐系统"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["本章代码涵盖了基于Python的解决方案，用于Coursera机器学习课程的第六个编程练习。 请参考[练习文本](ex8.pdf)了解详细的说明和公式。\n", "\n", "代码修改并注释：黄海广，<EMAIL>"]}, {"cell_type": "markdown", "metadata": {}, "source": ["在本练习中，我们将使用高斯模型实现异常检测算法，并将其应用于检测网络上的故障服务器。 我们还将看到如何使用协作过滤构建推荐系统，并将其应用于电影推荐数据集。"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Anomaly detection（异常检测）"]}, {"cell_type": "markdown", "metadata": {}, "source": ["我们的第一个任务是使用高斯模型来检测数据集中未标记的示例是否应被视为异常。 我们有一个简单的二维数据集开始，以帮助可视化该算法正在做什么。 "]}, {"cell_type": "code", "execution_count": 1, "metadata": {"collapsed": true}, "outputs": [], "source": ["import numpy as np\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "import seaborn as sb\n", "from scipy.io import loadmat"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"data": {"text/plain": ["(307, 2)"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["data = loadmat('data/ex8data1.mat')\n", "X = data['X']\n", "X.shape"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAsYAAAHRCAYAAABpU38LAAAABHNCSVQICAgIfAhkiAAAAAlwSFlz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\n", "text/plain": ["<matplotlib.figure.Figure at 0xa635710>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["fig, ax = plt.subplots(figsize=(12,8))\n", "ax.scatter(X[:,0], X[:,1])\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["这是一个非常紧密的聚类，几个值远离了聚类。 在这个简单的例子中，这些可以被认为是异常的。 为了弄清楚，我们正在为数据中的每个特征估计高斯分布。 为此，我们将创建一个返回每个要素的均值和方差的函数。"]}, {"cell_type": "code", "execution_count": 4, "metadata": {"collapsed": true}, "outputs": [], "source": ["def estimate_gaussian(X):\n", "    mu = X.mean(axis=0)\n", "    sigma = X.var(axis=0)\n", "    \n", "    return mu, sigma"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"text/plain": ["(array([ 14.11222578,  14.99771051]), array([ 1.83263141,  1.70974533]))"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["mu, sigma = estimate_gaussian(X)\n", "mu, sigma"]}, {"cell_type": "markdown", "metadata": {}, "source": ["现在我们有了我们的模型参数，我们需要确定概率阈值，这表明一个样本应该被认为是一个异常。 为此，我们需要使用一组标记的验证数据（其中真实异常样本已被标记），并在给出不同阈值的情况下，对模型的性能进行鉴定。"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"text/plain": ["((307, 2), (307, 1))"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["Xval = data['Xval']\n", "yval = data['yval']\n", "\n", "Xval.shape, yval.shape"]}, {"cell_type": "markdown", "metadata": {}, "source": ["我们还需要一种计算数据点属于正态分布的概率的方法。 幸运的是SciPy有这个内置的方法。"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"text/plain": ["0.1935875044615038"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["from scipy import stats\n", "dist = stats.norm(mu[0], sigma[0])\n", "dist.pdf(15)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["我们还可以将数组传递给概率密度函数，并获得数据集中每个点的概率密度。"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([ 0.183842  ,  0.20221694,  0.21746136,  0.19778763,  0.20858956,\n", "        0.21652359,  0.16991291,  0.15123542,  0.1163989 ,  0.1594734 ,\n", "        0.21716057,  0.21760472,  0.20141857,  0.20157497,  0.21711385,\n", "        0.21758775,  0.21695576,  0.2138258 ,  0.21057069,  0.1173018 ,\n", "        0.20765108,  0.21717452,  0.19510663,  0.21702152,  0.17429399,\n", "        0.15413455,  0.21000109,  0.20223586,  0.21031898,  0.21313426,\n", "        0.16158946,  0.2170794 ,  0.17825767,  0.17414633,  0.1264951 ,\n", "        0.19723662,  0.14538809,  0.21766361,  0.21191386,  0.21729442,\n", "        0.21238912,  0.18799417,  0.21259798,  0.21752767,  0.20616968,\n", "        0.21520366,  0.1280081 ,  0.21768113,  0.21539967,  0.16913173])"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["dist.pdf(X[:,0])[0:50]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["我们计算并保存给定上述的高斯模型参数的数据集中每个值的概率密度。"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"data": {"text/plain": ["(307, 2)"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["p = np.zeros((X.shape[0], X.shape[1]))\n", "p[:,0] = stats.norm(mu[0], sigma[0]).pdf(X[:,0])\n", "p[:,1] = stats.norm(mu[1], sigma[1]).pdf(X[:,1])\n", "\n", "p.shape"]}, {"cell_type": "markdown", "metadata": {}, "source": ["我们还需要为验证集（使用相同的模型参数）执行此操作。 我们将使用与真实标签组合的这些概率来确定将数据点分配为异常的最佳概率阈值。"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"data": {"text/plain": ["(307, 2)"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["pval = np.zeros((Xval.shape[0], Xval.shape[1]))\n", "pval[:,0] = stats.norm(mu[0], sigma[0]).pdf(Xval[:,0])\n", "pval[:,1] = stats.norm(mu[1], sigma[1]).pdf(Xval[:,1])\n", "\n", "pval.shape"]}, {"cell_type": "markdown", "metadata": {}, "source": ["接下来，我们需要一个函数，找到给定概率密度值和真实标签的最佳阈值。 为了做到这一点，我们将为不同的epsilon值计算F1分数。 F1是真阳性，假阳性和假阴性的数量的函数。 方程式在练习文本中。"]}, {"cell_type": "code", "execution_count": 11, "metadata": {"collapsed": true}, "outputs": [], "source": ["def select_threshold(pval, yval):\n", "    best_epsilon = 0\n", "    best_f1 = 0\n", "    f1 = 0\n", "    \n", "    step = (pval.max() - pval.min()) / 1000\n", "    \n", "    for epsilon in np.arange(pval.min(), pval.max(), step):\n", "        preds = pval < epsilon\n", "        \n", "        tp = np.sum(np.logical_and(preds == 1, yval == 1)).astype(float)\n", "        fp = np.sum(np.logical_and(preds == 1, yval == 0)).astype(float)\n", "        fn = np.sum(np.logical_and(preds == 0, yval == 1)).astype(float)\n", "        \n", "        precision = tp / (tp + fp)\n", "        recall = tp / (tp + fn)\n", "        f1 = (2 * precision * recall) / (precision + recall)\n", "        \n", "        if f1 > best_f1:\n", "            best_f1 = f1\n", "            best_epsilon = epsilon\n", "    \n", "    return best_epsilon, best_f1"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\anaconda3\\lib\\site-packages\\ipykernel_launcher.py:15: RuntimeWarning: invalid value encountered in double_scalars\n", "  from ipykernel import kernelapp as app\n"]}, {"data": {"text/plain": ["(0.0095667060059568421, 0.7142857142857143)"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["epsilon, f1 = select_threshold(pval, yval)\n", "epsilon, f1"]}, {"cell_type": "markdown", "metadata": {}, "source": ["最后，我们可以将阈值应用于数据集，并可视化结果。"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"data": {"text/plain": ["(array([300, 301, 301, 303, 303, 304, 306, 306], dtype=int64),\n", " array([1, 0, 1, 0, 1, 0, 0, 1], dtype=int64))"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["# indexes of the values considered to be outliers\n", "outliers = np.where(p < epsilon)\n", "outliers"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAr4AAAHRCAYAAACMzbpnAAAABHNCSVQICAgIfAhkiAAAAAlwSFlz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\nAAAj/P/hSJSiOz0WSgAAAABJRU5ErkJggg==\n", "text/plain": ["<matplotlib.figure.Figure at 0xa828828>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["fig, ax = plt.subplots(figsize=(12,8))\n", "ax.scatter(X[:,0], X[:,1])\n", "ax.scatter(X[outliers[0],0], X[outliers[0],1], s=50, color='r', marker='o')\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["红点是被标记为异常值的点。 这些看起来很合理。 有一些分离（但没有被标记）的右上角也可能是一个异常值，但是相当接近。"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 协同过滤"]}, {"cell_type": "markdown", "metadata": {}, "source": ["推荐引擎使用基于项目和用户的相似性度量来检查用户的历史偏好，以便为用户可能感兴趣的新“事物”提供建议。在本练习中，我们将实现一种称为协作过滤的特定推荐系统算法，并将其应用于 电影评分的数据集。\n", "\n", "我们首先加载并检查我们将要使用的数据。"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'R': array([[1, 1, 0, ..., 1, 0, 0],\n", "        [1, 0, 0, ..., 0, 0, 1],\n", "        [1, 0, 0, ..., 0, 0, 0],\n", "        ..., \n", "        [0, 0, 0, ..., 0, 0, 0],\n", "        [0, 0, 0, ..., 0, 0, 0],\n", "        [0, 0, 0, ..., 0, 0, 0]], dtype=uint8),\n", " 'Y': array([[5, 4, 0, ..., 5, 0, 0],\n", "        [3, 0, 0, ..., 0, 0, 5],\n", "        [4, 0, 0, ..., 0, 0, 0],\n", "        ..., \n", "        [0, 0, 0, ..., 0, 0, 0],\n", "        [0, 0, 0, ..., 0, 0, 0],\n", "        [0, 0, 0, ..., 0, 0, 0]], dtype=uint8),\n", " '__globals__': [],\n", " '__header__': b'MATLAB 5.0 MAT-file, Platform: GLNXA64, Created on: Thu Dec  1 17:19:26 2011',\n", " '__version__': '1.0'}"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["data = loadmat('data/ex8_movies.mat')\n", "data"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Y是包含从1到5的等级的（数量的电影x数量的用户）数组.R是包含指示用户是否给电影评分的二进制值的“指示符”数组。 两者应该具有相同的维度。"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"data": {"text/plain": ["((1682, 943), (1682, 943))"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["Y = data['Y']\n", "R = data['R']\n", "Y.shape, R.shape"]}, {"cell_type": "markdown", "metadata": {}, "source": ["我们可以通过平均排序Y来评估电影的平均评级。"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"data": {"text/plain": ["3.2061068702290076"]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["Y[1,np.where(R[1,:]==1)[0]].mean()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["我们还可以通过将矩阵渲染成图像来尝试“可视化”数据。 我们不能从这里收集太多，但它确实给我们了解用户和电影的相对密度。"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAf0AAANUCAYAAAC5QPSHAAAABHNCSVQICAgIfAhkiAAAAAlwSFlz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\nP6Y/+IM/SCml9Du/8zvp0UcfTT/****************************+N/33f/93evrpp9N//dd/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\nQNAHAACoCQR9AACAmkDQBwAAqIn/BxNaMuUXD2FCAAAAAElFTkSuQmCC\n", "text/plain": ["<matplotlib.figure.Figure at 0xa8babe0>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["fig, ax = plt.subplots(figsize=(12,12))\n", "ax.imshow(Y)\n", "ax.set_xlabel('Users')\n", "ax.set_ylabel('Movies')\n", "fig.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["接下来，我们将实施协同过滤的代价函数。 直觉上，“代价”是指一组电影评级预测偏离真实预测的程度。 代价方程在练习文本中给出。 它基于文本中称为X和Theta的两组参数矩阵。 这些“展开”到“参数”输入中，以便稍后可以使用SciPy的优化包。 请注意，我已经在注释中包含数组/矩阵形状（对于我们在本练习中使用的数据），以帮助说明矩阵交互如何工作。\n", "# cost\n", "<img style=\"float: left;\" src=\"../img/rcmd_cost.png\">"]}, {"cell_type": "code", "execution_count": 19, "metadata": {"collapsed": true}, "outputs": [], "source": ["def cost(params, Y, R, num_features):\n", "    Y = np.matrix(Y)  # (1682, 943)\n", "    R = np.matrix(R)  # (1682, 943)\n", "    num_movies = Y.shape[0]\n", "    num_users = Y.shape[1]\n", "    \n", "    # reshape the parameter array into parameter matrices\n", "    X = np.matrix(np.reshape(params[:num_movies * num_features], (num_movies, num_features)))  # (1682, 10)\n", "    Theta = np.matrix(np.reshape(params[num_movies * num_features:], (num_users, num_features)))  # (943, 10)\n", "    \n", "    # initializations\n", "    J = 0\n", "    \n", "    # compute the cost\n", "    error = np.multiply((X * Theta.T) - Y, R)  # (1682, 943)\n", "    squared_error = np.power(error, 2)  # (1682, 943)\n", "    J = (1. / 2) * np.sum(squared_error)\n", "    \n", "    return J"]}, {"cell_type": "markdown", "metadata": {}, "source": ["为了测试这一点，我们提供了一组我们可以评估的预训练参数。 为了保持评估时间的少点，我们将只看一小段数据。"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"data": {"text/plain": ["((1682, 10), (943, 10))"]}, "execution_count": 20, "metadata": {}, "output_type": "execute_result"}], "source": ["params_data = loadmat('data/ex8_movieParams.mat')\n", "X = params_data['X']\n", "Theta = params_data['Theta']\n", "X.shape, Theta.shape"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"data": {"text/plain": ["22.224603725685675"]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}], "source": ["users = 4\n", "movies = 5\n", "features = 3\n", "\n", "X_sub = X[:movies, :features]\n", "Theta_sub = Theta[:users, :features]\n", "Y_sub = Y[:movies, :users]\n", "R_sub = R[:movies, :users]\n", "\n", "params = np.concatenate((np.ravel(X_sub), np.ravel(Theta_sub)))\n", "\n", "cost(params, Y_sub, R_sub, features)"]}, {"cell_type": "markdown", "metadata": {}, "source": [" 接下来我们需要实现梯度计算。 就像我们在练习4中使用神经网络实现一样，我们将扩展代价函数来计算梯度。"]}, {"cell_type": "code", "execution_count": 22, "metadata": {"collapsed": true}, "outputs": [], "source": ["def cost(params, Y, R, num_features):\n", "    Y = np.matrix(Y)  # (1682, 943)\n", "    R = np.matrix(R)  # (1682, 943)\n", "    num_movies = Y.shape[0]\n", "    num_users = Y.shape[1]\n", "    \n", "    # reshape the parameter array into parameter matrices\n", "    X = np.matrix(np.reshape(params[:num_movies * num_features], (num_movies, num_features)))  # (1682, 10)\n", "    Theta = np.matrix(np.reshape(params[num_movies * num_features:], (num_users, num_features)))  # (943, 10)\n", "    \n", "    # initializations\n", "    J = 0\n", "    X_grad = np.zeros(X.shape)  # (1682, 10)\n", "    Theta_grad = np.zeros(Theta.shape)  # (943, 10)\n", "    \n", "    # compute the cost\n", "    error = np.multiply((X * Theta.T) - Y, R)  # (1682, 943)\n", "    squared_error = np.power(error, 2)  # (1682, 943)\n", "    J = (1. / 2) * np.sum(squared_error)\n", "    \n", "    # calculate the gradients\n", "    X_grad = error * Theta\n", "    Theta_grad = error.T * X\n", "    \n", "    # unravel the gradient matrices into a single array\n", "    grad = np.concatenate((np.ravel(X_grad), np.ravel(Theta_grad)))\n", "    \n", "    return J, grad"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [{"data": {"text/plain": ["(22.224603725685675,\n", " array([ -2.52899165,   7.57570308,  -1.89979026,  -0.56819597,\n", "          3.35265031,  -0.52339845,  -0.83240713,   4.91163297,\n", "         -0.76677878,  -0.38358278,   2.26333698,  -0.35334048,\n", "         -0.80378006,   4.74271842,  -0.74040871, -10.5680202 ,\n", "          4.62776019,  -7.16004443,  -3.05099006,   1.16441367,\n", "         -3.47410789,   0.        ,   0.        ,   0.        ,\n", "          0.        ,   0.        ,   0.        ]))"]}, "execution_count": 23, "metadata": {}, "output_type": "execute_result"}], "source": ["J, grad = cost(params, Y_sub, R_sub, features)\n", "J, grad"]}, {"cell_type": "markdown", "metadata": {}, "source": ["我们的下一步是在代价和梯度计算中添加正则化。 我们将创建一个最终的正则化版本的功能（请注意，此版本包含一个额外的“学习率”参数，在文本中称为“lambda”）。"]}, {"cell_type": "code", "execution_count": 24, "metadata": {"collapsed": true}, "outputs": [], "source": ["def cost(params, Y, R, num_features, learning_rate):\n", "    Y = np.matrix(Y)  # (1682, 943)\n", "    R = np.matrix(R)  # (1682, 943)\n", "    num_movies = Y.shape[0]\n", "    num_users = Y.shape[1]\n", "    \n", "    # reshape the parameter array into parameter matrices\n", "    X = np.matrix(np.reshape(params[:num_movies * num_features], (num_movies, num_features)))  # (1682, 10)\n", "    Theta = np.matrix(np.reshape(params[num_movies * num_features:], (num_users, num_features)))  # (943, 10)\n", "    \n", "    # initializations\n", "    J = 0\n", "    X_grad = np.zeros(X.shape)  # (1682, 10)\n", "    Theta_grad = np.zeros(Theta.shape)  # (943, 10)\n", "    \n", "    # compute the cost\n", "    error = np.multiply((X * Theta.T) - Y, R)  # (1682, 943)\n", "    squared_error = np.power(error, 2)  # (1682, 943)\n", "    J = (1. / 2) * np.sum(squared_error)\n", "    \n", "    # add the cost regularization\n", "    J = J + ((learning_rate / 2) * np.sum(np.power(Theta, 2)))\n", "    J = J + ((learning_rate / 2) * np.sum(np.power(X, 2)))\n", "    \n", "    # calculate the gradients with regularization\n", "    X_grad = (error * Theta) + (learning_rate * X)\n", "    Theta_grad = (error.T * X) + (learning_rate * Theta)\n", "    \n", "    # unravel the gradient matrices into a single array\n", "    grad = np.concatenate((np.ravel(X_grad), np.ravel(Theta_grad)))\n", "    \n", "    return J, grad"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [{"data": {"text/plain": ["(31.344056244274221,\n", " array([ -0.95596339,   6.97535514,  -0.10861109,   0.60308088,\n", "          2.77421145,   0.25839822,   0.12985616,   4.0898522 ,\n", "         -0.89247334,   0.29684395,   1.06300933,   0.66738144,\n", "          0.60252677,   4.90185327,  -0.19747928, -10.13985478,\n", "          2.10136256,  -6.76563628,  -2.29347024,   0.48244098,\n", "         -2.99791422,  -0.64787484,  -0.71820673,   1.27006666,\n", "          1.09289758,  -0.40784086,   0.49026541]))"]}, "execution_count": 25, "metadata": {}, "output_type": "execute_result"}], "source": ["J, grad = cost(params, Y_sub, R_sub, features, 1.5)\n", "J, grad"]}, {"cell_type": "markdown", "metadata": {}, "source": ["这个结果再次与练习代码的预期输出相匹配，所以看起来正则化是正常的。 在我们训练模型之前，我们有一个最后步骤， 我们的任务是创建自己的电影评分，以便我们可以使用该模型来生成个性化的推荐。 为我们提供一个连接电影索引到其标题的文件。 接着我们将文件加载到字典中。"]}, {"cell_type": "code", "execution_count": 26, "metadata": {"collapsed": true}, "outputs": [], "source": ["movie_idx = {}\n", "f = open('data/movie_ids.txt',encoding= 'gbk')\n", "for line in f:\n", "    tokens = line.split(' ')\n", "    tokens[-1] = tokens[-1][:-1]\n", "    movie_idx[int(tokens[0]) - 1] = ' '.join(tokens[1:])"]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [{"data": {"text/plain": ["'Toy Story (1995)'"]}, "execution_count": 27, "metadata": {}, "output_type": "execute_result"}], "source": ["movie_idx[0]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["我们将使用练习中提供的评分。"]}, {"cell_type": "code", "execution_count": 28, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Rated Toy Story (1995) with 4 stars.\n", "Rated Twelve Monkeys (1995) with 3 stars.\n", "Rated Usual Suspects, The (1995) with 5 stars.\n", "Rated Outbreak (1995) with 4 stars.\n", "Rated <PERSON><PERSON><PERSON> Redemption, The (1994) with 5 stars.\n", "Rated While You Were Sleeping (1995) with 3 stars.\n", "Rated <PERSON> (1994) with 5 stars.\n", "Rated Silence of the Lambs, The (1991) with 2 stars.\n", "Rated Alien (1979) with 4 stars.\n", "Rated Die Hard 2 (1990) with 5 stars.\n", "Rated Sphere (1998) with 5 stars.\n"]}], "source": ["ratings = np.zeros((1682, 1))\n", "\n", "ratings[0] = 4\n", "ratings[6] = 3\n", "ratings[11] = 5\n", "ratings[53] = 4\n", "ratings[63] = 5\n", "ratings[65] = 3\n", "ratings[68] = 5\n", "ratings[97] = 2\n", "ratings[182] = 4\n", "ratings[225] = 5\n", "ratings[354] = 5\n", "\n", "print('Rated {0} with {1} stars.'.format(movie_idx[0], str(int(ratings[0]))))\n", "print('Rated {0} with {1} stars.'.format(movie_idx[6], str(int(ratings[6]))))\n", "print('Rated {0} with {1} stars.'.format(movie_idx[11], str(int(ratings[11]))))\n", "print('Rated {0} with {1} stars.'.format(movie_idx[53], str(int(ratings[53]))))\n", "print('Rated {0} with {1} stars.'.format(movie_idx[63], str(int(ratings[63]))))\n", "print('Rated {0} with {1} stars.'.format(movie_idx[65], str(int(ratings[65]))))\n", "print('Rated {0} with {1} stars.'.format(movie_idx[68], str(int(ratings[68]))))\n", "print('Rated {0} with {1} stars.'.format(movie_idx[97], str(int(ratings[97]))))\n", "print('Rated {0} with {1} stars.'.format(movie_idx[182], str(int(ratings[182]))))\n", "print('Rated {0} with {1} stars.'.format(movie_idx[225], str(int(ratings[225]))))\n", "print('Rated {0} with {1} stars.'.format(movie_idx[354], str(int(ratings[354]))))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We can add our own ratings vector to the existing data set to include in the model.\n", "我们可以将自己的评级向量添加到现有数据集中以包含在模型中。"]}, {"cell_type": "code", "execution_count": 29, "metadata": {}, "outputs": [{"data": {"text/plain": ["((1682, 944), (1682, 944), (1682, 1))"]}, "execution_count": 29, "metadata": {}, "output_type": "execute_result"}], "source": ["R = data['R']\n", "Y = data['Y']\n", "\n", "Y = np.append(Y, ratings, axis=1)\n", "R = np.append(R, ratings != 0, axis=1)\n", "\n", "Y.shape, R.shape, ratings.shape"]}, {"cell_type": "markdown", "metadata": {}, "source": ["我们不只是准备训练协同过滤模型。 我们只需要定义一些变量并对评级进行规一化。"]}, {"cell_type": "code", "execution_count": 30, "metadata": {}, "outputs": [{"data": {"text/plain": ["((1682, 10), (944, 10), (26260,))"]}, "execution_count": 30, "metadata": {}, "output_type": "execute_result"}], "source": ["movies = Y.shape[0]  # 1682\n", "users = Y.shape[1]  # 944\n", "features = 10\n", "learning_rate = 10.\n", "\n", "X = np.random.random(size=(movies, features))\n", "Theta = np.random.random(size=(users, features))\n", "params = np.concatenate((np.ravel(X), np.ravel(Theta)))\n", "\n", "X.shape, Theta.shape, params.shape"]}, {"cell_type": "code", "execution_count": 31, "metadata": {}, "outputs": [{"data": {"text/plain": ["5.5070364565159845e-19"]}, "execution_count": 31, "metadata": {}, "output_type": "execute_result"}], "source": ["Ymean = np.zeros((movies, 1))\n", "Ynorm = np.zeros((movies, users))\n", "\n", "for i in range(movies):\n", "    idx = np.where(R[i,:] == 1)[0]\n", "    Ymean[i] = Y[i,idx].mean()\n", "    Ynorm[i,idx] = Y[i,idx] - Ymean[i]\n", "\n", "Ynorm.mean()"]}, {"cell_type": "code", "execution_count": 32, "metadata": {}, "outputs": [{"data": {"text/plain": ["     fun: 38960.83047850314\n", "     jac: array([-0.01842856,  0.00036187,  0.0039912 , ..., -0.00252333,\n", "       -0.00332469,  0.00360307])\n", " message: 'Maximum number of iterations has been exceeded.'\n", "    nfev: 153\n", "     nit: 100\n", "    njev: 153\n", "  status: 1\n", " success: <PERSON><PERSON><PERSON>\n", "       x: array([-0.19551694,  0.64277558,  0.02459796, ...,  0.14703959,\n", "       -0.1226612 ,  0.01494952])"]}, "execution_count": 32, "metadata": {}, "output_type": "execute_result"}], "source": ["from scipy.optimize import minimize\n", "\n", "fmin = minimize(fun=cost, x0=params, args=(Ynorm, R, features, learning_rate), \n", "                method='CG', jac=True, options={'maxiter': 100})\n", "fmin"]}, {"cell_type": "code", "execution_count": 33, "metadata": {}, "outputs": [{"data": {"text/plain": ["((1682, 10), (944, 10))"]}, "execution_count": 33, "metadata": {}, "output_type": "execute_result"}], "source": ["X = np.matrix(np.reshape(fmin.x[:movies * features], (movies, features)))\n", "Theta = np.matrix(np.reshape(fmin.x[movies * features:], (users, features)))\n", "\n", "X.shape, Theta.shape"]}, {"cell_type": "markdown", "metadata": {}, "source": ["我们训练好的参数是X和Theta。 我们可以使用这些来为我们添加的用户创建一些建议。"]}, {"cell_type": "code", "execution_count": 34, "metadata": {}, "outputs": [{"data": {"text/plain": ["(1682, 1)"]}, "execution_count": 34, "metadata": {}, "output_type": "execute_result"}], "source": ["predictions = X * Theta.T \n", "my_preds = predictions[:, -1] + <PERSON><PERSON>an\n", "my_preds.shape"]}, {"cell_type": "code", "execution_count": 35, "metadata": {}, "outputs": [{"data": {"text/plain": ["matrix([[ 5.00000029],\n", "        [ 5.00000016],\n", "        [ 4.99999988],\n", "        [ 4.99999986],\n", "        [ 4.99999972],\n", "        [ 4.99999963],\n", "        [ 4.99999955],\n", "        [ 4.99999923],\n", "        [ 4.99999919],\n", "        [ 4.99999914]])"]}, "execution_count": 35, "metadata": {}, "output_type": "execute_result"}], "source": ["sorted_preds = np.sort(my_preds, axis=0)[::-1]\n", "sorted_preds[:10]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["这给了我们一个排名最高的评级，但我们失去了这些评级的索引。 我们实际上需要使用argsort函数来预测评分对应的电影。"]}, {"cell_type": "code", "execution_count": 36, "metadata": {}, "outputs": [{"data": {"text/plain": ["matrix([[1292],\n", "        [1535],\n", "        [1188],\n", "        ..., \n", "        [ 783],\n", "        [1333],\n", "        [1358]], dtype=int64)"]}, "execution_count": 36, "metadata": {}, "output_type": "execute_result"}], "source": ["idx = np.argsort(my_preds, axis=0)[::-1]\n", "idx"]}, {"cell_type": "code", "execution_count": 37, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Top 10 movie predictions:\n", "Predicted rating of 5.00000028532343 for movie Star Kid (1997).\n", "Predicted rating of 5.000000164968406 for movie <PERSON><PERSON> wa<PERSON> (1994).\n", "Predicted rating of 4.999999875154623 for movie Prefontaine (1997).\n", "Predicted rating of 4.999999859773239 for movie They Made Me a Criminal (1939).\n", "Predicted rating of 4.999999720991511 for movie <PERSON>'s America (1995).\n", "Predicted rating of 4.999999630401129 for movie <PERSON><PERSON>: Shadow and Light (1996) .\n", "Predicted rating of 4.999999553416016 for movie Entertaining Angels: The Dorothy Day Story (1996).\n", "Predicted rating of 4.999999233308761 for movie Saint of Fort Washington, The (1993).\n", "Predicted rating of 4.999999188844037 for movie Great Day in Harlem, A (1994).\n", "Predicted rating of 4.999999143466484 for movie Santa with Muscles (1996).\n"]}], "source": ["print(\"Top 10 movie predictions:\")\n", "for i in range(10):\n", "    j = int(idx[i])\n", "    print('Predicted rating of {0} for movie {1}.'.format(str(float(my_preds[j])), movie_idx[j]))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["推荐的电影实际上并不符合练习文本中的内容， 我没有找到原因在哪里。"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.6.4"}}, "nbformat": 4, "nbformat_minor": 1}