import os
import shutil
from datetime import datetime

def rename_images():
    # 目标文件夹路径
    folder_path = r"D:\大学任务文档\武汉cdc大三实习\卫生应急部\25.7.15_卫生应急预案\演练剧本模板"
    
    # 支持的图片格式
    image_extensions = ('.jpg', '.jpeg', '.png', '.bmp', '.gif', '.tiff', '.webp')
    
    try:
        # 检查文件夹是否存在
        if not os.path.exists(folder_path):
            print(f"错误：文件夹不存在: {folder_path}")
            return
        
        # 获取所有图片文件及其修改时间
        image_files = []
        for filename in os.listdir(folder_path):
            if filename.lower().endswith(image_extensions):
                file_path = os.path.join(folder_path, filename)
                if os.path.isfile(file_path):
                    mod_time = os.path.getmtime(file_path)
                    ext = os.path.splitext(filename)[1]
                    image_files.append((file_path, mod_time, ext, filename))
        
        if not image_files:
            print("未找到图片文件")
            return
        
        print(f"找到 {len(image_files)} 个图片文件")
        
        # 按修改时间排序（从早到晚）
        image_files.sort(key=lambda x: x[1])
        
        # 显示文件列表
        print("\n按修改时间排序:")
        for i, (_, mod_time, _, filename) in enumerate(image_files, 1):
            time_str = datetime.fromtimestamp(mod_time).strftime('%Y-%m-%d %H:%M:%S')
            print(f"{i:2d}. {filename} | {time_str}")
        
        # 确认操作
        confirm = input("\n确认重命名? (y/n): ")
        if confirm.lower() != 'y':
            print("操作取消")
            return
        
        # 创建备份文件夹
        backup_folder = os.path.join(folder_path, f"backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}")
        os.makedirs(backup_folder)
        print(f"\n备份文件夹: {backup_folder}")
        
        # 备份并重命名
        for i, (file_path, _, ext, old_name) in enumerate(image_files, 1):
            # 备份原文件
            shutil.copy2(file_path, os.path.join(backup_folder, old_name))
            
            # 新文件名
            new_name = f"{i}{ext}"
            new_path = os.path.join(folder_path, new_name)
            
            # 重命名
            try:
                if os.path.exists(new_path) and new_path != file_path:
                    os.remove(new_path)
                os.rename(file_path, new_path)
                print(f"重命名: {old_name} -> {new_name}")
            except Exception as e:
                print(f"重命名失败: {old_name}, 错误: {e}")
        
        print(f"\n完成! 文件已按时间顺序重命名为 1{ext} 到 {len(image_files)}{ext}")
        
    except Exception as e:
        print(f"程序错误: {e}")
    
    input("\n按回车键退出...")
    

if __name__ == "__main__":
    rename_images()
