{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 机器学习练习 5 - 偏差和方差"]}, {"cell_type": "markdown", "metadata": {}, "source": ["本章代码涵盖了基于Python的解决方案，用于Coursera机器学习课程的第五个编程练习。 请参考[练习文本](ex5.pdf)了解详细的说明和公式。\n", "\n", "代码修改并注释：黄海广，<EMAIL>"]}, {"cell_type": "code", "execution_count": 17, "metadata": {"collapsed": true}, "outputs": [], "source": ["import numpy as np\n", "import scipy.io as sio\n", "import scipy.optimize as opt\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns"]}, {"cell_type": "code", "execution_count": 18, "metadata": {"collapsed": true}, "outputs": [], "source": ["def load_data():\n", "    \"\"\"for ex5\n", "    d['X'] shape = (12, 1)\n", "    pandas has trouble taking this 2d ndarray to construct a dataframe, so I ravel\n", "    the results\n", "    \"\"\"\n", "    d = sio.loadmat('ex5data1.mat')\n", "    return map(np.ravel, [d['X'], d['y'], d['Xval'], d['yval'], d['Xtest'], d['ytest']])"]}, {"cell_type": "code", "execution_count": 19, "metadata": {"collapsed": true}, "outputs": [], "source": ["X, y, Xval, yval, Xtest, ytest = load_data()"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 700x500 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# 方法2：使用 scatterplot 替代 lmplot\n", "plt.figure(figsize=(7, 5))\n", "sns.scatterplot(x='water_level', y='flow', data=df)\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 21, "metadata": {"collapsed": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["X shape: (12,)\n", "y shape: (12,)\n", "Xval shape: (21,)\n", "yval shape: (21,)\n", "Xtest shape: (21,)\n", "ytest shape: (21,)\n"]}], "source": ["# 重新加载原始数据\n", "X, y, Xval, yval, Xtest, ytest = load_data()\n", "\n", "# 检查数据形状\n", "print(\"X shape:\", X.shape)\n", "print(\"y shape:\", y.shape)\n", "print(\"Xval shape:\", Xval.shape)\n", "print(\"yval shape:\", yval.shape)\n", "print(\"Xtest shape:\", Xtest.shape)\n", "print(\"ytest shape:\", ytest.shape)\n", "\n", "# 添加偏置项（截距项）\n", "X, Xval, Xtest = [np.insert(x.reshape(x.shape[0], 1), 0, np.ones(x.shape[0]), axis=1) for x in (X, Xval, Xtest)]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 代价函数\n", "<img style=\"float: left;\" src=\"../img/linear_cost.png\">"]}, {"cell_type": "code", "execution_count": 22, "metadata": {"collapsed": true}, "outputs": [], "source": ["def cost(theta, X, y):\n", "    \"\"\"\n", "    X: R(m*n), m records, n features\n", "    y: R(m)\n", "    theta : R(n), linear regression parameters\n", "    \"\"\"\n", "    m = X.shape[0]\n", "\n", "    inner = X @ theta - y  # R(m*1)\n", "\n", "    # 1*m @ m*1 = 1*1 in matrix multiplication\n", "    # but you know n<PERSON>y didn't do transpose in 1d array, so here is just a\n", "    # vector inner product to itselves\n", "    square_sum = inner.T @ inner\n", "    cost = square_sum / (2 * m)\n", "\n", "    return cost"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [{"data": {"text/plain": ["303.95152555359766"]}, "execution_count": 23, "metadata": {}, "output_type": "execute_result"}], "source": ["theta = np.ones(X.shape[1])\n", "cost(theta, X, y)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 梯度\n", "<img style=\"float: left;\" src=\"../img/linear_gradient.png\">"]}, {"cell_type": "code", "execution_count": 24, "metadata": {"collapsed": true}, "outputs": [], "source": ["def gradient(theta, X, y):\n", "    m = X.shape[0]\n", "\n", "    inner = X.T @ (X @ theta - y)  # (m,n).T @ (m, 1) -> (n, 1)\n", "\n", "    return inner / m"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([-15.30301567, 598.16741084])"]}, "execution_count": 25, "metadata": {}, "output_type": "execute_result"}], "source": ["gradient(theta, X, y)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 正则化梯度\n", "<img style=\"float: left;\" src=\"../img/linear_reg_gradient.png\">"]}, {"cell_type": "code", "execution_count": 26, "metadata": {"collapsed": true}, "outputs": [], "source": ["def regularized_gradient(theta, X, y, l=1):\n", "    m = X.shape[0]\n", "\n", "    regularized_term = theta.copy()  # same shape as theta\n", "    regularized_term[0] = 0  # don't regularize intercept theta\n", "\n", "    regularized_term = (l / m) * regularized_term\n", "\n", "    return gradient(theta, X, y) + regularized_term"]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([-15.30301567, 598.25074417])"]}, "execution_count": 27, "metadata": {}, "output_type": "execute_result"}], "source": ["regularized_gradient(theta, X, y)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 拟合数据\n", "> 正则化项 $\\lambda=0$"]}, {"cell_type": "code", "execution_count": 28, "metadata": {"collapsed": true}, "outputs": [], "source": ["def linear_regression_np(X, y, l=1):\n", "    \"\"\"linear regression\n", "    args:\n", "        X: feature matrix, (m, n+1) # with incercept x0=1\n", "        y: target vector, (m, )\n", "        l: lambda constant for regularization\n", "\n", "    return: trained parameters\n", "    \"\"\"\n", "    # init theta\n", "    theta = np.ones(X.shape[1])\n", "\n", "    # train it\n", "    res = opt.minimize(fun=regularized_cost,\n", "                       x0=theta,\n", "                       args=(X, y, l),\n", "                       method='TNC',\n", "                       jac=regularized_gradient,\n", "                       options={'disp': True})\n", "    return res\n"]}, {"cell_type": "code", "execution_count": 29, "metadata": {"collapsed": true}, "outputs": [], "source": ["def regularized_cost(theta, X, y, l=1):\n", "    m = X.shape[0]\n", "\n", "    regularized_term = (l / (2 * m)) * np.power(theta[1:], 2).sum()\n", "\n", "    return cost(theta, X, y) + regularized_term"]}, {"cell_type": "code", "execution_count": 30, "metadata": {"collapsed": true}, "outputs": [], "source": ["theta = np.ones(X.shape[0])\n", "\n", "final_theta = linear_regression_np(X, y, l=0).get('x')"]}, {"cell_type": "code", "execution_count": 31, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["b = final_theta[0] # intercept\n", "m = final_theta[1] # slope\n", "\n", "plt.scatter(X[:,1], y, label=\"Training data\")\n", "plt.plot(X[:, 1], X[:, 1]*m + b, label=\"Prediction\")\n", "plt.legend(loc=2)\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 32, "metadata": {"collapsed": true}, "outputs": [], "source": ["training_cost, cv_cost = [], []"]}, {"cell_type": "markdown", "metadata": {}, "source": ["1.使用训练集的子集来拟合应模型\n", "\n", "2.在计算训练代价和交叉验证代价时，没有用正则化\n", "\n", "3.记住使用相同的训练集子集来计算训练代价"]}, {"cell_type": "code", "execution_count": 33, "metadata": {"collapsed": true}, "outputs": [], "source": ["m = X.shape[0]\n", "for i in range(1, m+1):\n", "#     print('i={}'.format(i))\n", "    res = linear_regression_np(X[:i, :], y[:i], l=0)\n", "    \n", "    tc = regularized_cost(res.x, X[:i, :], y[:i], l=0)\n", "    cv = regularized_cost(res.x, Xval, yval, l=0)\n", "#     print('tc={}, cv={}'.format(tc, cv))\n", "    \n", "    training_cost.append(tc)\n", "    cv_cost.append(cv)"]}, {"cell_type": "code", "execution_count": 34, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plt.plot(np.arange(1, m+1), training_cost, label='training cost')\n", "plt.plot(np.arange(1, m+1), cv_cost, label='cv cost')\n", "plt.legend(loc=1)\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["这个模型拟合不太好, **欠拟合了**"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 创建多项式特征"]}, {"cell_type": "code", "execution_count": 41, "metadata": {"collapsed": true}, "outputs": [], "source": ["def prepare_poly_data(*args, power):\n", "    \"\"\"\n", "    args: keep feeding in X, Xval, or Xtest\n", "        will return in the same order\n", "    \"\"\"\n", "    def prepare(x):\n", "        # expand feature\n", "        df = poly_features(x, power=power)\n", "\n", "        # normalization\n", "        ndarr = normalize_feature(df).values  # 修改：使用 .values 替代 .as_matrix()\n", "\n", "        # add intercept term\n", "        return np.insert(ndarr, 0, np.ones(ndarr.shape[0]), axis=1)\n", "\n", "    return [prepare(x) for x in args]"]}, {"cell_type": "code", "execution_count": 42, "metadata": {"collapsed": true}, "outputs": [], "source": ["def poly_features(x, power, as_ndarray=False):\n", "    data = {'f{}'.format(i): np.power(x, i) for i in range(1, power + 1)}\n", "    df = pd.DataFrame(data)\n", "\n", "    return df.values if as_ndarray else df  # 修改：使用 .values 替代 .as_matrix()"]}, {"cell_type": "code", "execution_count": 43, "metadata": {"collapsed": true}, "outputs": [], "source": ["X, y, Xval, yval, Xtest, ytest = load_data()"]}, {"cell_type": "code", "execution_count": 44, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>f1</th>\n", "      <th>f2</th>\n", "      <th>f3</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>-15.936758</td>\n", "      <td>253.980260</td>\n", "      <td>-4047.621971</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>-29.152979</td>\n", "      <td>849.896197</td>\n", "      <td>-24777.006175</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>36.189549</td>\n", "      <td>1309.683430</td>\n", "      <td>47396.852168</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>37.492187</td>\n", "      <td>1405.664111</td>\n", "      <td>52701.422173</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>-48.058829</td>\n", "      <td>2309.651088</td>\n", "      <td>-110999.127750</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>-8.941458</td>\n", "      <td>79.949670</td>\n", "      <td>-714.866612</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>15.307793</td>\n", "      <td>234.328523</td>\n", "      <td>3587.052500</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>-34.706266</td>\n", "      <td>1204.524887</td>\n", "      <td>-41804.560890</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>1.389154</td>\n", "      <td>1.929750</td>\n", "      <td>2.680720</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>-44.383760</td>\n", "      <td>1969.918139</td>\n", "      <td>-87432.373590</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>7.013502</td>\n", "      <td>49.189211</td>\n", "      <td>344.988637</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>22.762749</td>\n", "      <td>518.142738</td>\n", "      <td>11794.353058</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["           f1           f2             f3\n", "0  -15.936758   253.980260   -4047.621971\n", "1  -29.152979   849.896197  -24777.006175\n", "2   36.189549  1309.683430   47396.852168\n", "3   37.492187  1405.664111   52701.422173\n", "4  -48.058829  2309.651088 -110999.127750\n", "5   -8.941458    79.949670    -714.866612\n", "6   15.307793   234.328523    3587.052500\n", "7  -34.706266  1204.524887  -41804.560890\n", "8    1.389154     1.929750       2.680720\n", "9  -44.383760  1969.918139  -87432.373590\n", "10   7.013502    49.189211     344.988637\n", "11  22.762749   518.142738   11794.353058"]}, "execution_count": 44, "metadata": {}, "output_type": "execute_result"}], "source": ["poly_features(X, power=3)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 准备多项式回归数据\n", "1. 扩展特征到 8阶,或者你需要的阶数\n", "2. 使用 **归一化** 来合并 $x^n$ \n", "3. don't forget intercept term"]}, {"cell_type": "code", "execution_count": 45, "metadata": {"collapsed": true}, "outputs": [], "source": ["def normalize_feature(df):\n", "    \"\"\"Applies function along input axis(default 0) of DataFrame.\"\"\"\n", "    return df.apply(lambda column: (column - column.mean()) / column.std())"]}, {"cell_type": "code", "execution_count": 46, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([[ 1.00000000e+00, -3.62140776e-01, -7.55086688e-01,\n", "         1.82225876e-01, -7.06189908e-01,  3.06617917e-01,\n", "        -5.90877673e-01,  3.44515797e-01, -5.08481165e-01],\n", "       [ 1.00000000e+00, -8.03204845e-01,  1.25825266e-03,\n", "        -2.47936991e-01, -3.27023420e-01,  9.33963187e-02,\n", "        -4.35817606e-01,  2.55416116e-01, -4.48912493e-01],\n", "       [ 1.00000000e+00,  1.37746700e+00,  5.84826715e-01,\n", "         1.24976856e+00,  2.45311974e-01,  9.78359696e-01,\n", "        -1.21556976e-02,  7.56568484e-01, -1.70352114e-01]])"]}, "execution_count": 46, "metadata": {}, "output_type": "execute_result"}], "source": ["X_poly, Xval_poly, Xtest_poly= prepare_poly_data(X, Xval, Xtest, power=8)\n", "X_poly[:3, :]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 画出学习曲线\n", "> 首先，我们没有使用正则化，所以 $\\lambda=0$"]}, {"cell_type": "code", "execution_count": 47, "metadata": {"collapsed": true}, "outputs": [], "source": ["def plot_learning_curve(X, y, Xval, yval, l=0):\n", "    training_cost, cv_cost = [], []\n", "    m = X.shape[0]\n", "\n", "    for i in range(1, m + 1):\n", "        # regularization applies here for fitting parameters\n", "        res = linear_regression_np(X[:i, :], y[:i], l=l)\n", "\n", "        # remember, when you compute the cost here, you are computing\n", "        # non-regularized cost. Regularization is used to fit parameters only\n", "        tc = cost(res.x, X[:i, :], y[:i])\n", "        cv = cost(res.x, Xval, yval)\n", "\n", "        training_cost.append(tc)\n", "        cv_cost.append(cv)\n", "\n", "    plt.plot(np.arange(1, m + 1), training_cost, label='training cost')\n", "    plt.plot(np.arange(1, m + 1), cv_cost, label='cv cost')\n", "    plt.legend(loc=1)\n"]}, {"cell_type": "code", "execution_count": 48, "metadata": {}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAigAAAGdCAYAAAA44ojeAAAAOXRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjcuMCwgaHR0cHM6Ly9tYXRwbG90bGliLm9yZy88F64QAAAACXBIWXMAAA9hAAAPYQGoP6dpAABChklEQVR4nO3deXxU9b3/8ddkmywkYc8QEjAsCrLJfgUULAKioohiFVCpXVxATWlRKVXRalJQKUoqCu0Vqkb89SpKXYmIUUQlAhEECqJhJwQVk7BlPb8/TmaSEBISMpNzJnk/H495zJkzZ858MmLmne/5nO9xGIZhICIiImIjAVYXICIiInI6BRQRERGxHQUUERERsR0FFBEREbEdBRQRERGxHQUUERERsR0FFBEREbEdBRQRERGxnSCrCzgXpaWlHDx4kMjISBwOh9XliIiISC0YhkF+fj6xsbEEBNQ8RuKXAeXgwYPEx8dbXYaIiIicg3379hEXF1fjNn4ZUCIjIwHzB4yKirK4GhEREamNvLw84uPjPd/jNfHLgOI+rBMVFaWAIiIi4mdq056hJlkRERGxHQUUERERsR0FFBEREbEdv+xBERERaxmGQXFxMSUlJVaXIjYTHBxMYGBgvfejgCIiInVSWFjIoUOHOHHihNWliA05HA7i4uJo1qxZvfajgCIiIrVWWlpKVlYWgYGBxMbGEhISogkzxcMwDI4cOcL+/fvp2rVrvUZSFFBERKTWCgsLKS0tJT4+nvDwcKvLERtq06YNu3fvpqioqF4BRU2yIiJSZ2ebplyaLm+NqOlfmIiIiNiOAoqIiMg5OO+881iwYEGtt//4449xOBz8/PPPPqupMVFAERGRJmHEiBEkJiZ6bX8ZGRn87ne/q/X2Q4YM4dChQ0RHR3uthoa2e/duHA4HmZmZPn8vNcmKiIiUMQyDkpISgoLO/vXYpk2bOu07JCQEl8t1rqU1OQooFR07Ap88CY4AcDjMeyh7XHGdo5rHFbatdptz2a+j6rq4gRDVruE/IxERPzR16lTS09NJT0/nmWeeASArK4vdu3dz2WWX8f777zN79mw2b97MBx98QIcOHZgxYwZffPEFx48fp3v37iQnJ3P55Zd79nneeeeRmJjoGZVxOBwsWbKEd955hw8++ID27dvz9NNPc8011wDmIZ7LLruMo0eP0rx5c5YuXUpiYiKvvfYaiYmJ7Nu3j2HDhvHiiy/Srp35+724uJgZM2bwr3/9i8DAQH7zm9+QnZ1Nbm4ub775ZrU/72effcaf/vQnMjIycDqdDBo0iOXLl9OiRQsKCgqYOXMmy5cvJy8vjwEDBvC3v/2NgQMHAnD06FGmT5/OqlWrOHbsGHFxcfzpT3/iV7/6FQkJCQD07dsXgOHDh/Pxxx978z+VhwJKRad+hvUvWF1F7bTsDPdsMIOLiIiFDMPgZJE1M8qGBQfW6qyRZ555hp07d9KzZ08ee+wxoPx0WID777+fp556ik6dOtG8eXP279/PlVdeyeOPP05oaCjLli1j3Lhx7Nixgw4dOlT7Po8++ijz5s3jySefZOHChUyePJk9e/bQsmXLM25/4sQJnnrqKV566SUCAgKYMmUKf/zjH3nllVcAmDt3Lq+88govvvgi3bt355lnnuHNN9/ksssuq7aGzMxMRo4cye23386zzz5LUFAQa9as8cz6e//99/P666+zbNkyOnbsyLx58xgzZgy7du2iZcuWPPTQQ2zbto333nuP1q1bs2vXLk6ePAnA+vXrGTRoEB9++CE9evQgJCTkrJ/9uVJAqSisBVzyR8AAoxQM932p+XzFdadvQ4VtDWqxjXGW/Z6+zih/vO9L+Ok7yN0PzeMb/nMSEangZFEJFz78gSXvve2xMYSHnP2rLDo6mpCQEMLDw894mOWxxx5j1KhRnsetWrWiT58+nsePP/44K1asYOXKlUyfPr3a95k6dSo333wzAElJSSxcuJD169dzxRVXnHH7oqIinn/+eTp37gzA9OnTPQEKYOHChcyaNYvrrrsOgJSUFN59990af9Z58+YxYMAAnnvuOc+6Hj16AHD8+HEWLVrE0qVLGTt2LABLliwhLS2Nf/7zn8ycOZO9e/fSt29fBgwYAJgjRW7uw1qtWrXy+eEqBZSKIlrDyIesruLsXrgUDn0NB75SQBER8QL3l7Hb8ePHefTRR3n77bc5ePAgxcXFnDx5kr1799a4n969e3uWIyIiiIyMJCcnp9rtw8PDPeEEoF27dp7tc3NzOXz4MIMGDfI8HxgYSP/+/SktLa12n5mZmUycOPGMz3333XcUFRUxdOhQz7rg4GAGDRrE9u3bAbjrrru4/vrr2bhxI6NHj2b8+PEMGTKkxp/bFxRQ/FH7AWZA2f8V9LjO6mpEpIkLCw5k22NjLHtvb4iIiKj0eObMmXzwwQc89dRTdOnShbCwMG644QYKCwtr3E9wcHClxw6Ho8YwcabtDcOosq6i058/XVhYWLXPuV97pn26140dO5Y9e/bwzjvv8OGHHzJy5EimTZvGU089VeP7eptOM/ZHcWVJ/8AGa+sQEcH8sgsPCbLkVpdZS0NCQmp99eVPP/2UqVOnct1119GrVy9cLpenX6WhREdHExMTw/r16z3rSkpK2LRpU42v6927N6tXrz7jc126dCEkJIS1a9d61hUVFfHVV1/RvXt3z7o2bdowdepUXn75ZRYsWMDixYsBPD0nDXEVa42g+KP2ZQHlYCaUFEFgcI2bi4iI2Uvx5Zdfsnv3bpo1a1Zt4yqYX+RvvPEG48aNw+Fw8NBDD9U4EuIr99xzD8nJyXTp0oVu3bqxcOFCjh49WmMwmzVrFr169eLuu+/mzjvvJCQkhDVr1jBx4kRat27NXXfdxcyZM2nZsiUdOnRg3rx5nDhxgl//+tcAPPzww/Tv358ePXpQUFDA22+/7Qkvbdu2JSwsjPfff5+4uDhCQ0N9Nq+LRlD8UasuEBoNxSfh8FarqxER8Qt//OMfCQwM5MILL6RNmzY19pP87W9/o0WLFgwZMoRx48YxZswY+vXr14DVmh544AFuvvlmbr31Vi6++GKaNWvGmDFjCA0NrfY1559/PqtWreLrr79m0KBBXHzxxbz11lueuV3++te/cv3113PLLbfQr18/du3axQcffECLFi0Ac5Rk1qxZ9O7dm0svvZTAwECWL18OQFBQEM8++ywvvPACsbGxXHvttT772R3G2Q5m2VBeXh7R0dHk5uYSFRVldTnWeOk6+O4juOppGPgbq6sRkSbi1KlTZGVlkZCQUOOXpPhGaWkp3bt358Ybb+Qvf/mL1eWcUU3/Rury/a0RFH/lPsyzX30oIiKN1Z49e1iyZAk7d+5ky5Yt3HXXXWRlZTFp0iSrS/M5BRR/5WmU/craOkRExGcCAgJYunQpAwcOZOjQoWzZsoUPP/ywUkNrY6UmWX/Vvr95/8NOOPkzhDW3shoREfGB+Ph4PvvsM6vLsIRGUPxVRGtoYV4TQacbi4hIY6OA4s80H4qIiDRSCij+zNMoqz4UERFpXBRQ/FnFRln/O1tcRESkWgoo/szVCwJD4MSPcHS31dWIiIh4jQKKPwtymiEF1IciIiKNSp0DyieffMK4ceOIjY3F4XDw5ptvVnreMAzmzJlDbGwsYWFhjBgxgq1bK0/HXlBQwD333EPr1q2JiIjgmmuuYf/+/fX6QZqsuIHm/f4Ma+sQERHxojoHlOPHj9OnTx9SUlLO+Py8efOYP38+KSkpZGRk4HK5GDVqFPn5+Z5tEhMTWbFiBcuXL2ft2rUcO3aMq6++ukGujtjoqFFWRKTR+fjjj3E4HPz8889Wl2KZOk/UNnbsWMaOHXvG5wzDYMGCBcyePZsJEyYAsGzZMmJiYkhNTeWOO+4gNzeXf/7zn7z00ktcfvnlALz88svEx8fz4YcfMmbMmHr8OE1QXNmEbdmbobjAPOwjIiLi57zag5KVlUV2djajR4/2rHM6nQwfPpx169YBsGHDBoqKiiptExsbS8+ePT3bnK6goIC8vLxKNynTIgHCW0FJIWR/Y3U1IiK2VVpayty5c+nSpQtOp5MOHTrwxBNPAHDxxRfz4IMPVtr+yJEjBAcHs2bNmmr3uXLlSgYMGEBoaCitW7f2/HEOcPToUW699VZatGhBeHg4Y8eO5dtvv/U8v2fPHsaNG0eLFi2IiIigR48evPvuu+zevZvLLrsMgBYtWuBwOJg6daoXPwn/4NWAkp2dDUBMTEyl9TExMZ7nsrOzCQkJ8VzW+UzbnC45OZno6GjPLT4+3ptl+zeHo3zae12XR0SsYBhQeNyaWx2mWJg1axZz587loYceYtu2baSmpnq+ryZPnsyrr76KUWF/r732GjExMQwfPvyM+3vnnXeYMGECV111FZs2bWL16tUMGDDA8/zUqVP56quvWLlyJZ9//jmGYXDllVdSVFQEwLRp0ygoKOCTTz5hy5YtzJ07l2bNmhEfH8/rr78OwI4dOzh06BDPPPNMnf+z+DufXIvH4XBUemwYRpV1p6tpm1mzZjFjxgzP47y8PIWUiuIGwrerzEbZwXdYXY2INDVFJyAp1pr3/tNBCIk462b5+fk888wzpKSkcNtttwHQuXNnhg0bBsAvf/lLfv/737N27VouueQSAFJTU5k0aRIBAWf+W/6JJ57gpptu4tFHH/Ws69OnDwDffvstK1eu5LPPPmPIkCEAvPLKK8THx/Pmm28yceJE9u7dy/XXX0+vXubZmJ06dfLsp2XLlgC0bduW5s2b1+UTaTS8OoLicrkAqoyE5OTkeFKqy+WisLCQo0ePVrvN6ZxOJ1FRUZVuUoF7BEWNsiIiZ7R9+3YKCgoYOXLkGZ9v06YNo0aN4pVXXgHMloXPP/+cyZMnV7vPzMzMave3fft2goKCGDx4sGddq1atuOCCC9i+fTsA9957L48//jhDhw7lkUceYfPmzef64zVKXh1BSUhIwOVykZaWRt++fQEoLCwkPT2duXPnAtC/f3+Cg4NJS0vjxhtvBODQoUN88803zJs3z5vlNB3ugHI0C47/CBGtrK1HRJqW4HBzJMOq966FsLCws24zefJk7rvvPhYuXEhqaio9evTwjIjUdZ9GNYeeKh4t+M1vfsOYMWN45513WLVqFcnJyTz99NPcc889Z621KajzCMqxY8fIzMwkMzMTMFNmZmYme/fuxeFwkJiYSFJSEitWrOCbb75h6tSphIeHM2nSJACio6P59a9/zR/+8AdWr17Npk2bmDJlCr169fKc1SN1FNYcWnU1lzVhm4g0NIfDPMxixe0s7QNuXbt2JSwsjNWrV1e7zfjx4zl16hTvv/8+qampTJkypcZ99u7du9r9XXjhhRQXF/Pll1961v3444/s3LmT7t27e9bFx8dz55138sYbb/CHP/yBJUuWABASEgLQtKffMOpozZo1BlDldttttxmGYRilpaXGI488YrhcLsPpdBqXXnqpsWXLlkr7OHnypDF9+nSjZcuWRlhYmHH11Vcbe/furXUNubm5BmDk5ubWtfzG6407DOORKMP46AmrKxGRRuzkyZPGtm3bjJMnT1pdSp3NmTPHaNGihbFs2TJj165dxueff2784x//qLTNpEmTjD59+hgOh8PYs2dPjftbs2aNERAQYDz88MPGtm3bjM2bNxtz5871PH/ttdcaF154ofHpp58amZmZxhVXXGF06dLFKCwsNAzDMO677z7j/fffN77//ntjw4YNxqBBg4wbb7zRMAzD2L9/v+FwOIylS5caOTk5Rn5+vpc/Dd+p6d9IXb6/6xxQ7EAB5QzWLzEDyr/GW12JiDRi/hxQSkpKjMcff9zo2LGjERwcbHTo0MFISkqqtM0777xjAMall15aq32+/vrrxkUXXWSEhIQYrVu3NiZMmOB57qeffjJuueUWIzo62ggLCzPGjBlj7Ny50/P89OnTjc6dOxtOp9No06aNccsttxg//PCD5/nHHnvMcLlchsPh8AwC+ANvBRSHYfjfZXDz8vKIjo4mNzdXDbNuBzNh8XAIjYb7d0M1XeciIvVx6tQpsrKySEhIIDQ01OpyxIZq+jdSl+9vfYs1FjE9ICgUTuXCT99ZXY2IiEi9KKA0FoHB0O4ic1mnG4uIiJ9TQGlM4spmMNSMsiIi4ucUUBoTTdgmIiKNhAJKYxI30Lw//A0UnbS2FhERkXpQQGlMouOgWQyUFsOhr62uRkQaMT88AVQaiLf+bSigNCYOB7Qv60PRYR4R8YHg4GAATpw4YXElYleFhYUABAYG1ms/PrmasVgorj/seEeNsiLiE4GBgTRv3pycnBwAwsPDz3q1emk6SktLOXLkCOHh4QQF1S9iKKA0Np4RFF2TR0R8w33lendIEakoICCADh061Du4KqA0Nu37AQ7I3Qv5hyEyxuqKRKSRcTgctGvXjrZt21JUVGR1OWIzISEhBHhhNnMFlMbGGQltu0PONvMwT7errK5IRBqpwMDAevcZiFRHTbKNkeZDERERP6eA0hhpRlkREfFzCiiNkbtR9sAmKC2xthYREZFzoIDSGLXtDsERUJgPR3ZYXY2IiEidKaA0RgGBZWfzoMM8IiLilxRQGis1yoqIiB9TQGmsPI2ymrBNRET8jwJKY+VulM3ZBgXHrK1FRESkjhRQGquodhDVHoxSOJRpdTUiIiJ1ooDSmLkP8+zPsLYOERGROlJAacw8Fw5Uo6yIiPgXBZTGTI2yIiLipxRQGrN2F4EjEPIPQe4Bq6sRERGpNQWUxiwkHGIuNJc1YZuIiPgRBZTGLm6gea9GWRER8SMKKI2dp1FWfSgiIuI/FFAaO3ej7KFMKCm2tBQREZHaUkBp7Fp1BWc0FJ0wZ5UVERHxAwoojV1AALTvay6rUVZERPyEAkpT4GmUVR+KiIj4BwWUpqC9prwXERH/ooDSFLgbZX/YCadyra1FRESkFhRQmoKI1tC8I2DAgY1WVyMiInJWCihNhee6PGqUFRER+1NAaSo0YZuIiPgRBZSmouKU94ZhbS0iIiJnoYDSVLh6QUAwnPgBft5jdTUiIiI1UkBpKoJDzZACsF99KCIiYm8KKE2Jp1FWfSgiImJvCihNiadRViMoIiJibwooTYnnysZfQ3GhtbWIiIjUQAGlKWnZCcJaQkkBHN5idTUiIiLVUkBpShwOaN/fXNZ8KCIiYmMKKE2NZpQVERE/oIDS1KhRVkRE/IACSlPTvp95/9N3cOIna2sRERGphgJKUxPeElp1MZc1H4qIiNiUAkpTpMM8IiJicwooTZEaZUVExOYUUJoi96nGBzboysYiImJLXg8oxcXF/PnPfyYhIYGwsDA6derEY489RmlpqWcbwzCYM2cOsbGxhIWFMWLECLZu3ertUqQ6MT0h0Aknj8JP31tdjYiISBVeDyhz587l+eefJyUlhe3btzNv3jyefPJJFi5c6Nlm3rx5zJ8/n5SUFDIyMnC5XIwaNYr8/HxvlyNnEhQCsReZy/szLC1FRETkTLweUD7//HOuvfZarrrqKs477zxuuOEGRo8ezVdfmf0OhmGwYMECZs+ezYQJE+jZsyfLli3jxIkTpKamerscqY4aZUVExMa8HlCGDRvG6tWr2blzJwBff/01a9eu5corrwQgKyuL7OxsRo8e7XmN0+lk+PDhrFu37oz7LCgoIC8vr9JN6inO3YeigCIiIvYT5O0dPvDAA+Tm5tKtWzcCAwMpKSnhiSee4OabbwYgOzsbgJiYmEqvi4mJYc+ePWfcZ3JyMo8++qi3S23a3CMo2d9A0SkIDrW2HhERkQq8PoLy2muv8fLLL5OamsrGjRtZtmwZTz31FMuWLau0ncPhqPTYMIwq69xmzZpFbm6u57Zv3z5vl930NO8AEW2gtAiyN1tdjYiISCVeH0GZOXMmDz74IDfddBMAvXr1Ys+ePSQnJ3PbbbfhcrkAcySlXbt2ntfl5ORUGVVxczqdOJ1Ob5fatDkcEDcQdrxrNsrGD7K6IhEREQ+vj6CcOHGCgIDKuw0MDPScZpyQkIDL5SItLc3zfGFhIenp6QwZMsTb5UhN3POhqFFWRERsxusjKOPGjeOJJ56gQ4cO9OjRg02bNjF//nxuv/12wDy0k5iYSFJSEl27dqVr164kJSURHh7OpEmTvF2O1EQzyoqIiE15PaAsXLiQhx56iLvvvpucnBxiY2O54447ePjhhz3b3H///Zw8eZK7776bo0ePMnjwYFatWkVkZKS3y5GaxPYDHPDzXjh2BJq1sboiERERAByG4X9znefl5REdHU1ubi5RUVFWl+Pf/j4YjvwXbl4OF4y1uhoREWnE6vL9rWvxNHVxmrBNRETsRwGlqfPMKKsp70VExD4UUJo69wjKwU1Q4YKOIiIiVlJAaeradIfgcCjIgx92Wl2NiIgIoIAigUEQ29dc1unGIiJiEwooognbRETEdhRQxJzyHhRQRETENhRQpLxRNmcrFB63thYREREUUAQgKhYiY8EohYOZVlcjIiKigCJl4sr6UNQoKyIiNqCAIqb2mlFWRETsQwFFTGqUFRERG1FAEVPsReAIhPyDkHfQ6mpERKSJU0ARU0gEtL3QXNYoioiIWEwBRcqpUVZERGxCAUXKeRplN1hbh4iINHkKKFLO3Sh7cBOUFFtbi4iINGkKKFKu9fngjIKi43Bku9XViIhIE6aAIuUCAsqvbKxGWRERsZACilTmvi6PGmVFRMRCCihSmRplRUTEBhRQpDL3CMqR/8KpPGtrERGRJksBRSpr1haadwAMOLjR6mpERKSJUkCRqnThQBERsZgCilTlaZRVH4qIiFhDAUWqqjiCYhjW1iIiIk2SAopU1a43BATB8RzI3Wd1NSIi0gQpoEhVwWHg6mUu78+wthYREWmSFFDkzDQfioiIWEgBRc5MM8qKiIiFFFDkzNwjKIe+hpIia2sREZEmRwFFzqxVZwhtDsWn4PA3VlcjIiJNjAKKnJnDUX6YRxO2iYhIA1NAkeppRlkREbGIAopUT42yIiJiEQUUqV77/ub9j7vg5FFraxERkSZFAUWqF94SWnYyl3VdHhERaUAKKFIzTdgmIiIWUECRmsUNNO815b2IiDQgBRSpWVxZH8qBDbqysYiINBgFFKlZTC8IdMLJn+Cn762uRkREmggFFKlZUAi0620uq1FWREQaiAKKnJ0mbBMRkQamgCJn55nyXo2yIiLSMBRQ5OzcASV7CxSdsrYWERFpEhRQ5Oyad4Tw1lBaZIYUERERH1NAkbOreGVjXZdHREQagAKK1I4aZUVEpAEpoEjtaARFREQakAKK1E77foADju6G4z9YXY2IiDRyCihSO6HR0Pp8c1mHeURExMcUUKT2dJhHREQaiE8CyoEDB5gyZQqtWrUiPDyciy66iA0byqdJNwyDOXPmEBsbS1hYGCNGjGDr1q2+KEW8qX3ZhQM1giIiIj7m9YBy9OhRhg4dSnBwMO+99x7btm3j6aefpnnz5p5t5s2bx/z580lJSSEjIwOXy8WoUaPIz8/3djniTZ4RlI1QWmptLSIi0qgFeXuHc+fOJT4+nhdffNGz7rzzzvMsG4bBggULmD17NhMmTABg2bJlxMTEkJqayh133OHtksRb2vaAoDAoyIUfv4U2F1hdkYiINFJeH0FZuXIlAwYMYOLEibRt25a+ffuyZMkSz/NZWVlkZ2czevRozzqn08nw4cNZt27dGfdZUFBAXl5epZtYIDAIYvuayzrMIyIiPuT1gPL999+zaNEiunbtygcffMCdd97Jvffey7/+9S8AsrOzAYiJian0upiYGM9zp0tOTiY6Otpzi4+P93bZUltxZX0oapQVEREf8npAKS0tpV+/fiQlJdG3b1/uuOMOfvvb37Jo0aJK2zkcjkqPDcOoss5t1qxZ5Obmem779u3zdtlSW41lRtniAliTDNvesroSERE5A6/3oLRr144LL7yw0rru3bvz+uuvA+ByuQBzJKVdu3aebXJycqqMqrg5nU6cTqe3S5Vz4W6UPbwVCk9ASLi19ZyL4kL491TY8S6ENIMLroTAYKurEhGRCrw+gjJ06FB27NhRad3OnTvp2LEjAAkJCbhcLtLS0jzPFxYWkp6ezpAhQ7xdjnhbVHuIbAdGCRzKtLqauispgv/7lRlOAAqPwcFMS0sSEZGqvB5Qfv/73/PFF1+QlJTErl27SE1NZfHixUybNg0wD+0kJiaSlJTEihUr+Oabb5g6dSrh4eFMmjTJ2+WItzkc/jsfSkkR/N/t8N+3IdBZPjPu7k+trUtERKrwekAZOHAgK1as4NVXX6Vnz5785S9/YcGCBUyePNmzzf33309iYiJ33303AwYM4MCBA6xatYrIyEhvlyO+4I8zypYUw+u/ge0rITAEbkqFAbebz+1ea21tIiJShcMwDMPqIuoqLy+P6OhocnNziYqKsrqcpifrU1h2NUTFwQw/mAG4pBhW/A6+eR0CguGmV+D8MZC9BZ4fBsER8OAe9aGIiPhYXb6/dS0eqbvYvuAIgLz9kH/mU8Nto7QE3ryzPJz88iUznIA58Vxocyg6rj4UERGbUUCRunM2g7ZlZ2rZuQ+ltATevBu2/BsCgmDiUrhgbPnzAQFw3jBzWX0oIiK2ooAi58bTKJthbR3VKS2FlffA5uXgCIQb/he6X111O09AUR+KiIidKKDIufE0ym6oeTsrlJbCf+6BzFfKwsk/4cJrz7ytO6Ds/cI8y0dERGxBAUXOjXtG2YObzEMpdlFaCm8nwqaXzT6Z65dAj+uq375iH8qhrxuqShEROQsFFDk3bS4wZ2EtPAZH/mt1NSbDgHf/ABuXmeHkusXQ8/qaX6M+FBERW1JAkXMTEGivKxsbBrw7E776X8AB45+H3hNr91r1oYiI2I4Cipy7uIHmvdWNsoYB7z8IGUsww8lz0OeXtX+9+lBERGxHAUXOnR0aZQ0DPpgNXz5vPr5mIVxUx0smuPtQCo+pD0VExCYUUOTcuRtlc7ZDQX7Dv79hQNpD8MXfzcfjnoV+t9R9P+pDERGxHQUUOXeRMRAdDxjm2TwNyTDgwzmwbqH5+Oq/Qf/bzn1/6kMREbEVBRSpHyuubGwY8NFf4LMF5uMrnyq/8N+5Uh+KiIitKKBI/bgbZRuyD2VNEnz6tLk8dh4M+m3996k+FBERW1FAkfpxN8ruzzBHNnzt47/CJ/PM5THJMPgO7+w3IAA6DjWX1YciImI5BRSpn3Z9zAvxHTsMuft9+17pT8LHyeby6Mfh4ru9u3/1oYiI2IYCitRPcBjE9DCXD/iwD+XTp2HN4+by5Y/CkHu8/x7qQxERsQ0FFKk/9+nGvmqUXbsAVj9mLo98GIYl+uZ9YnqqD0VExCYUUKT+fNkouy4FPnzEXL7sz3DJH7z/Hm7qQxERsQ0FFKm/uApXNvbmoZHPn4NVs83lEbNg+Ezv7bs66kMREbEFBRSpv5adITQaik/B4a3e2eeXL8AHs8zlS++HEQ96Z79nU6kPpbhh3lNERKpQQJH6Cwgon7DNG42y65fAe/eby5f8AS77U/33WVvqQxERsQUFFPEOT6NsPftQvvpfePeP5vLQRPjFQ+Bw1G+fdaE+FBERW1BAEe/wXNm4HiMoG5bC2783l4fcA5fPadhw4qY+FBERyymgiHe4R1B+2Aknj9b99Rtfgv/cZy7/zzQY9RdrwglU6EP5XH0oIiIWUUAR74hoBS0SzOUDG+v22sxUWFk28drgO2HME9aFE1AfioiIDSigiPd4DvPUoQ/l6+Xw5t2AAQN/C1f81dpwAupDERGxAQUU8Z66zii7+d/w5l2AAQNuhyuftD6cuKkPRUTEUgoo4j0VG2XPdmXjb16HFb8DoxT63QZXPm2fcALqQxERsZgCiniPqxcEhsCJH+Ho7uq327oCXv+tGU763gJXLzAPq9iJ+lBERCxls28F8WtBTnD1NperO8yzbSX836/BKIGLJsO4Z+0XTqCsD2WIuaw+FBGRBmfDbwbxazXNh7L9bfi/X5nhpPdNcM1Ce4YTN/WhiIhYxsbfDuKXqmuU3fEe/HsqlBZDrxth/HMQENjg5dWJ+lBERCyjgCLeFVd2TZ7szVBcYC7v/ABeuwVKi6Dn9TB+kf3DCZT1oUSrD0VExAIKKOJdLRIgvBWUFEL2N/BtGrw2xQwnPa6D6xZDYJDVVdZOQKDmQxERsYgCiniXw1F+mOezBbB8shlWul8DE5b4TzhxUx+KiIglFFDE+9yNsttXQkkBdLsabvhfCAy2tq5z4elD+UJ9KCIiDUgBRbyvff/y5QuuhBte9M9wAhX6UPIhW30oIiINRQFFvK/jUDjvEugzCSYuhaAQqys6d5X6UHSYR0SkoSigiPcFh8LUt+G6Rebkbf5OfSgiIg1OAUXkbNwBZY/mQxERaSgKKCJnoz4UEZEGp4AicjbqQxERaXAKKCK1oT4UEZEGpYAiUhvqQxERaVAKKCK1oT4UEZEGpYAiUhvqQxERaVAKKCK1pYAiItJgFFBEakt9KCIiDUYBRaS2XL3AqT4UEZGGoIAiUlsBgdBxiLmswzwiIj6lgCJSF5oPRUSkQSigiNSFO6Ds/UJ9KCIiPuTzgJKcnIzD4SAxMdGzzjAM5syZQ2xsLGFhYYwYMYKtW7f6uhSR+nP3oRTkQfZmq6sREWm0fBpQMjIyWLx4Mb179660ft68ecyfP5+UlBQyMjJwuVyMGjWK/Px8X5YjUn/qQxERaRA+CyjHjh1j8uTJLFmyhBYtWnjWG4bBggULmD17NhMmTKBnz54sW7aMEydOkJqa6qtyRLxHfSgiIj7ns4Aybdo0rrrqKi6//PJK67OyssjOzmb06NGedU6nk+HDh7Nu3boz7qugoIC8vLxKNxHLePpQNB+KiIiv+CSgLF++nI0bN5KcnFzluezsbABiYmIqrY+JifE8d7rk5GSio6M9t/j4eO8XLVJb6kMREfE5rweUffv2cd999/Hyyy8TGhpa7XYOh6PSY8MwqqxzmzVrFrm5uZ7bvn37vFqzSJ2oD0VExOe8HlA2bNhATk4O/fv3JygoiKCgINLT03n22WcJCgryjJycPlqSk5NTZVTFzel0EhUVVekmYin1oYiI+JTXA8rIkSPZsmULmZmZntuAAQOYPHkymZmZdOrUCZfLRVpamuc1hYWFpKenM2TIEG+XI+Ib6kMREfGpIG/vMDIykp49e1ZaFxERQatWrTzrExMTSUpKomvXrnTt2pWkpCTCw8OZNGmSt8sR8Q1PH0qu2YfSvp/VFYmINCpeDyi1cf/993Py5Enuvvtujh49yuDBg1m1ahWRkZFWlCNSd+4+lJ3vmYd5FFBERLzKYRiGYXURdZWXl0d0dDS5ubnqRxHrrFsIq/4MXcfA5P9ndTUiIrZXl+9vXYtH5FypD0VExGcUUETOlas3OKM0H4qIiA8ooIicK82HIiLiMwooIvWh+VBERHxCAUWkPir2oZSWWFuLiEgjooAiUh/qQxER8QkFFJH6UB+KiIhPKKCI1Jf6UEREvE4BRaS+3AFlzzr1oYiIeIkCikh9qQ9FRMTrFFBE6kt9KCIiXqeAIuIN6kMREfEqBRQRb1AfioiIVymgiHiD+lBERLxKAUXEG9SHIiLiVQooIt7Scah5r4AiIlJvCigi3qI+FBERr1FAEfEW9aGIiHiNAoqItwQGQYeLzWUd5hERqRcFFBFv0nwoIiJeoYAi4k2ePpTP1YciIlIPCigi3uTpQ8mF7C1WVyMi4rcUUES8SX0oIiJeoYAi4m3qQxERqTcFFBFv03woIiL1poAi4m3qQxERqTcFFBFvUx+KiEi9KaCI+IL6UERE6kUBRcQX1IciIlIvCigivqA+FBGRelFAEfEF9aGIiNSLAoqIr5w31LxXQBERqTMFFBFfUR+KiMg5U0AR8RVXHwiJVB+KiMg5UEAR8ZXAIOioPhQRkXOhgCLiS5oPRUTknCigiPiSO6DsVR+KiEhdKKCI+JK7D+VULhz+xupqRET8hgKKiC+pD0VE5JwooIj4mvpQRETqTAFFxNc886F8pj4UEZFaUkAR8TX1oYiI1JkCioivqQ9FRKTOFFBEGoL6UERE6kQBRaQhqA9FRKROFFBEGoL6UERE6kQBRaQhqA9FRKROFFBEGor6UEREak0BRaShdFQfiohIbSmgiDSUdn0gpJn6UEREakEBRaShBAZBB/WhiIjUhgKKSENSH0rD+24NpAyCl2+AH3ZZXY2I1JLXA0pycjIDBw4kMjKStm3bMn78eHbs2FFpG8MwmDNnDrGxsYSFhTFixAi2bt3q7VJE7Oe8S8z7PeugtNTaWhq7whPw7v3w0nj4YQfsSoNFQ+DjuVBcYHV1InIWXg8o6enpTJs2jS+++IK0tDSKi4sZPXo0x48f92wzb9485s+fT0pKChkZGbhcLkaNGkV+fr63yxGxF08fys/qQ/Gl/V/BC5fA+hfMx/1/BZ1HQkkBfJxkBpWsT6ytUURq5DAMw/DlGxw5coS2bduSnp7OpZdeimEYxMbGkpiYyAMPPABAQUEBMTExzJ07lzvuuOOs+8zLyyM6Oprc3FyioqJ8Wb6I9718g/nX/JhkuPhuq6tpXIoL4ZN58OnTYJRCZCxcmwJdRoJhwNY34P1ZcOywuX3vm2D049CsjbV1izQRdfn+9nkPSm5uLgAtW7YEICsri+zsbEaPHu3Zxul0Mnz4cNatW3fGfRQUFJCXl1fpJuK31IfiGznb4R8j4ZMnzXDS60a4e50ZTgAcDuh5PUxbDwN/Azhg83JIGQAblumQm4jN+DSgGIbBjBkzGDZsGD179gQgOzsbgJiYmErbxsTEeJ47XXJyMtHR0Z5bfHy8L8sW8S1PH8pn+lL0htISWLcQXhgO2ZshrAVMXArXLzGXTxfWHK56Gn6zGly9zMNt/7kXXhwLh7c1cPEiUh2fBpTp06ezefNmXn311SrPORyOSo8Nw6iyzm3WrFnk5uZ6bvv27fNJvSINQn0o3nN0NywbB6v+bPaXdB0Dd38BPa47+2vj+sNvP4YxSRAcAfu+MPtW0h4xG2xFxFI+Cyj33HMPK1euZM2aNcTFxXnWu1wugCqjJTk5OVVGVdycTidRUVGVbiJ+S/Oh1J9hwMZ/waKh5khUSDMY9yxMeg0iXbXfT2AQXDwNpq+HbldDaTF8tgCeGww7V/msfBE5O68HFMMwmD59Om+88QYfffQRCQkJlZ5PSEjA5XKRlpbmWVdYWEh6ejpDhgzxdjki9qQ+lHOXfxhevQlW3gOFx8ywd+da6H+b2WdyLqLj4KZX4KZXISoOft4LqRPh/90KeYe8W7+I1EqQt3c4bdo0UlNTeeutt4iMjPSMlERHRxMWFobD4SAxMZGkpCS6du1K165dSUpKIjw8nEmTJnm7HBF7Or0PJUBzJtbKtrfgP4lw8icIDIFfPGSOgAQEemf/3a6EhEsh/a/w+XPm++36CEY+ZDbWeut9ROSsvH6acXV9JC+++CJTp04FzFGWRx99lBdeeIGjR48yePBg/v73v3saac9GpxmL3ysphrkdzRGAOz6Fdr2trsjeTv4M790Pm18zH7t6wXWLIeZC371n9hZ4+/ewP8N83O4iGLcAYvv67j1FGrm6fH/7fB4UX1BAkUZB86HUzncfwVvTIe8AOAJg2AwY/gAEhfj+vUtLYcOL8OGjUJBrvv+g38FlsyFUv3tE6spW86CISDXUh1KzwhPw7kx46ToznLTsDLevMg+3NEQ4AfPQ28Bfw/QM6DXRnF/ly+fh74PMwz/+9/ediN9QQBGxijugaD6UqjxT1S82Hw/8Ldz5KcQPtKaeyBi4/h9wywpo2QnyD5kNtKk3wtE91tQk0sgpoIhYRfOhVFVcCB89Dv8cBT/uMqeqn/IGXPUUhERYXR10/gXctQ4uvR8CguHbVfD3wbD2b1BSZHV1Io2KAoqIVQKDocP/mMs6zGPO4lrTVPV2ERwGv5htBpXzLoHik/DhHHjhUtj7pdXViTQaCigiVlIfijlV/WfPwmL3VPUtYeKy6qeqt4s258Nt/4Hxz0N4K8jZBv87GlbeCyd+sro6Eb+ngCJipaZ+XZ6ju2Hp1ZD2EJQUlk1V/zn0GG91ZbXjcMBFN8P0r6DvLea6jcsgZSB8/ZqaaEXqQQFFxEoV+1BytlpdTcMxDPMKwouGwt515z5VvV2Et4RrU+BX70GbbnDiB1jxO/jXNfDDLqurE/FLCigiVmqKfSj5hyH1l+YVhAuPQYch9Z+q3i46DjEn3hv5MASFQtYnsOhiWJMMRaesrk7EryigiFitKfWhbH0Tnvsf+PYDc6r6UX+BqW9Dy4SzvtRvBIXAJX8wr6rcZZR56Cr9r7BoCHyfbnV1In5DAUXEau4+lN1rG28fysmj8Ppv4d+3mdfRcfWC36XD0Hsb7/VtWibA5H/DxKXQzAU/fWce8nnjd3DsiNXVidieAoqI1Rp7H8p3H8FzQ2DL/zOnir90JvzmI99eR8cuHA7ocR1MX29OkY/DvJ5QSn/46sXGG0hFvMDrVzMWkTpy96Hs+tAcRXH1sroi7yg8DmmPQMYS83HLznDdC9bNBmul0Gi48knoczO8nQiHvjbvv34Vrv4bxPRomDoMw/zvUpAHp/Iq3OfCqdzT1lW8r/BceCuzEbjNBeX3rc+HkPCG+RmkydDFAkXsYO3fzMm+ul0NN71idTX1ty8DVtxhHtYAc/Tg8jn2mA3WaiXFZmj76HGzSTggCC6eZl4AsabPxzCg6GR5cDiVWxYsaggTnvBR4bFR4oMfygEtOp4huFwAzmY+eD/xV7qasYi/2f+VOYtqaHO4P8u8SJ0/Ki6E9Lmwdr45G2xkLIz/uzlFvFSWewDefwC2/8d8HN0BLhgLBfkVgkZu5fBRWuyd93YEmldjdkaZ96HNy5dPvw+NLluONg9FHjsMR3bAke3mfc52s6+oOtEdykKLO7h0Mye5C432zs8ifkUBRcTflBTB3PPMv6jv9NPDPIe3mXN/ZG8xH/f+JYyda+/ZYO1gx/vw7h8hd1/ttncEVAgR0dWEiaiq21R8Ljjcu6d0H/8BjvzXvOWU3R/ZAcdzqn9NZCy07VZ11EX/Xhq1unx/qwdFxA78uQ+ltAQ+TzEPWZQUmlPVX/03/5kN1moXXAEJl8CGpeYX/emjFqcHjpBm9psvJqI1RAwrP2Xe7cRPZaMt/61w22FeDTr/oHn77qPKr2kWUxZYulcedYlo1XA/T1NnGOYFTAvyzbl9LKKAImIX5w0rDyj/c5fV1ZxdcSHseAc+/zvszzDXnX+FOSNsZIy1tfmbkAizD6WxCW8JHS82bxWd/Bl+2FkeWHLKDhfl7TcPIR07bE5yV2lfrctHWdpWCC8RbewX2PzRsSNmWHTfjudAbF/43ceWlaSAImIXHStM2FZaat8+lB+/M//az0w1p3QH86/6K5LN69Hoy0LOJqw5xA8ybxWdyoMfvj1txOW/8PNe89/anrXmrdK+Wpx2mKgbuHprxOVsigtg35ewa7UZSLI3V34+ONycv6ekGAKtiQoKKCJ2EXsRBEeUz4dip8M8Rafgv2+bwWT3p+Xrm7mg7xQYcDtEt7esPGkkQqMgrr95q6jweNmIy47KfS5Hd5uTAO793LxV1KoLxA8uC0KDzTOK7Br6G4JhwI+7zDCya7X5h1DR8crbuHqbDe1dRpqfWZDTmlrLKKCI2IW7D+W71fbpQzmyw7yo39ep5hcBAA7oOgr6TzWvPmzRX1fShIREmIcbYvtWXl90smzEpUKfS8528/T2H3eZt8yy0/ad0eYcPO7Q0r4/OCMb/mdpSCd/hqz0slGSNZC7t/LzEW3LA0mnEdCsrRVVVku/WUTs5Lxh5QHFqj6UopPmNXM2Lqv8V2lUe/MQTt8p0DzemtpEKgoOg3a9zVtFJ34y+6L2fQn71sOBDeZ8MLs+NG9gng0V06MssJSFluYd/fsQZUkxHNxYPkpy4CvzdH+3wBDzj6DOI81Q0raHrUeVFFBE7MR9XZ49nzV8H8rhreYhnM2vmfNvgDlfxvlXmFca7nJ5471ujjQu4S3h/DHmDcwv7sPfmGHFHVpy95qnxGdvgYx/mNs1izGDSlzZYaF2fSA41LqfozZ+3mf+UfPdR/D9x+X/77q1Pt8MJJ1/AecN9avJEjUPioidlBTBXzuax4bv/AxcPX37foXH4Zs3zGBy4Kvy9dEdoP+tcNEUiGrn2xpErJB3sCywlIWWQ19DaVHlbQJDoN1F5X0s8YOtP0Ot8Djs/swMJbtWw4/fVn4+NNo8XOMOJTYb7dQ8KCL+qkofio8CysFM8xDO5n9DYb65LiAILrjS7C3pdJmth35F6i0q1pyrxz1fT9FJ8/8L9wjLvi/NM4f2rzdvn6eY2zXvWLn5tu2Fvu3DKi01R3/coyR7vzDnG3JzBEDcwPJA0r5foxnpVEARsRtPH8qn8D93em+/Bfmw5f/M0ZJDmeXrWySYh3Aummy7JjmRBhMcVnnOFsOAn76v3MtyeCv8vMe8bfl/5nYhzcyGW/cIS9wA8zTq+jiWYza1flfW3Hr6jLzRHaDLL8xQknBp/d/PphRQROzGm30ohgEHNsLGpbDl9fLTCgOCofs4c7TkvEs0WiJyOocDWnU2b31uMtedyjUbbt0jLPu/Mq+RlJVu3tzadK9wWGiQecpzTc23xQXmyMh3H5mhxH25CLfgCHO24c5loaRVZ/9u5q0lBRQRu3HPh3LyKORsO7fDPCd/hi3/Nk8RPlzhl12rruZoSZ+bzenJRaT2QqPLQkLZxS9LS8xTmyseFvrp+7ILKW43D6OCefkH98R08YMhth/kHSg7/dc9J8mJyu/l6m2eadN5pPk6i+cksYICiojdnGsfimGYvyQ3LIWtK6D4ZNn+nOZx9n63mdfVaAJ/eYk0iIBA81TlmB7mZIVgThm/f315A+7BjebVnne+b94AcACnnZ/SLKY8/HS6DJq1acifxJYUUETsqC59KCd+Mk8N3rDU/GvOrU138xBO7xvN0y5FxPeatYFuV5k3MK9Zlb2lbJSl7JZ/qGxOkovLRkl+ATE99cfDaRRQROzobH0ohmE+t2EZbHsLSgrM9UFh0HOCOVoSP0i/8ESsFhRSPn3/xXeb/+8eO2xenTok3OrqbE0BRcSOqutDOf6DeZG+jf+qPP9BTC+zt6TXxEbb0S/SKDgcEOmyugq/oIAiYkcV+1CyPjHnY9iwFLa/XT6ZVHAE9LrePIwT20+jJSLSqCigiNiVuw9l1ezK19OI7Wsewul1Q+O/2JmINFkKKCJ21Wk4rMYMJyGRZrNr/9vM64OIiDRyCigidtW+P1y32AwoF17jVxf5EhGpLwUUETvr80urKxARsYTmtxYRERHbUUARERER21FAEREREdtRQBERERHbUUARERER21FAEREREdtRQBERERHbUUARERER21FAEREREdtRQBERERHbUUARERER21FAEREREdtRQBERERHbUUARERER21FAEREREdtRQBERERHbsTSgPPfccyQkJBAaGkr//v359NNPrSxHREREbMKygPLaa6+RmJjI7Nmz2bRpE5dccgljx45l7969VpUkIiIiNuEwDMOw4o0HDx5Mv379WLRokWdd9+7dGT9+PMnJyTW+Ni8vj+joaHJzc4mKivJ1qSIiIuIFdfn+DmqgmiopLCxkw4YNPPjgg5XWjx49mnXr1lXZvqCggIKCAs/jvLw8n9T1w7EC/r5ml0/2LSLWsebPMN+p7d+Vtf2xa/v5GBgYhrlf8zVljw0oNQzPegNzI6Os1tIKy2Uv8+yr1DjzPivvr/xnrvya8te691nqg//Wvv473uFwmPeex2X3eBbKt61mG8/jSts6qqw70/tV3l/5Nq7oUJKu61W3H8aLLAkoP/zwAyUlJcTExFRaHxMTQ3Z2dpXtk5OTefTRR31eV97JIl78bLfP30dERMTuOreJsPT9LQkobo7TYp1hGFXWAcyaNYsZM2Z4Hufl5REfH+/1epqHhzDtss5e36+ISG04qPr7r8o2Z9nkrHs4yw7O9nqHw6zTvC977HCceX3ZY4AAR8XnypdxmD915efLPouK2wMBAZX36V7v3j7A4f7xzv451tXZPvdzVT44Y1R6bFR5vmxk6ozbVB3hKd/mtNec9tqKrz+tFCJDLY0I1gSU1q1bExgYWGW0JCcnp8qoCoDT6cTpdPq8rpYRIcwc083n7yMiIiI1s+QsnpCQEPr3709aWlql9WlpaQwZMsSKkkRERMRGLBu/mTFjBrfccgsDBgzg4osvZvHixezdu5c777zTqpJERETEJiwLKL/85S/58ccfeeyxxzh06BA9e/bk3XffpWPHjlaVJCIiIjZh2Two9aF5UERERPxPXb6/dS0eERERsR0FFBEREbEdBRQRERGxHQUUERERsR0FFBEREbEdBRQRERGxHQUUERERsR0FFBEREbEdBRQRERGxHWuvpXyO3JPf5uXlWVyJiIiI1Jb7e7s2k9j7ZUDJz88HID4+3uJKREREpK7y8/OJjo6ucRu/vBZPaWkpBw8eJDIyEofDYXU5DS4vL4/4+Hj27dunaxHVgz5H79Dn6B36HL1Dn6N3+OpzNAyD/Px8YmNjCQioucvEL0dQAgICiIuLs7oMy0VFRel/QC/Q5+gd+hy9Q5+jd+hz9A5ffI5nGzlxU5OsiIiI2I4CioiIiNiOAoofcjqdPPLIIzidTqtL8Wv6HL1Dn6N36HP0Dn2O3mGHz9Evm2RFRESkcdMIioiIiNiOAoqIiIjYjgKKiIiI2I4CioiIiNiOAoqfSE5OZuDAgURGRtK2bVvGjx/Pjh07rC7L7yUnJ+NwOEhMTLS6FL9z4MABpkyZQqtWrQgPD+eiiy5iw4YNVpflV4qLi/nzn/9MQkICYWFhdOrUiccee4zS0lKrS7O9Tz75hHHjxhEbG4vD4eDNN9+s9LxhGMyZM4fY2FjCwsIYMWIEW7dutaZYm6rpMywqKuKBBx6gV69eREREEBsby6233srBgwcbrD4FFD+Rnp7OtGnT+OKLL0hLS6O4uJjRo0dz/Phxq0vzWxkZGSxevJjevXtbXYrfOXr0KEOHDiU4OJj33nuPbdu28fTTT9O8eXOrS/Mrc+fO5fnnnyclJYXt27czb948nnzySRYuXGh1abZ3/Phx+vTpQ0pKyhmfnzdvHvPnzyclJYWMjAxcLhejRo3yXMtNav4MT5w4wcaNG3nooYfYuHEjb7zxBjt37uSaa65puAIN8Us5OTkGYKSnp1tdil/Kz883unbtaqSlpRnDhw837rvvPqtL8isPPPCAMWzYMKvL8HtXXXWVcfvtt1daN2HCBGPKlCkWVeSfAGPFihWex6WlpYbL5TL++te/etadOnXKiI6ONp5//nkLKrS/0z/DM1m/fr0BGHv27GmQmjSC4qdyc3MBaNmypcWV+Kdp06Zx1VVXcfnll1tdil9auXIlAwYMYOLEibRt25a+ffuyZMkSq8vyO8OGDWP16tXs3LkTgK+//pq1a9dy5ZVXWlyZf8vKyiI7O5vRo0d71jmdToYPH866dessrMy/5ebm4nA4Gmyk1C8vFtjUGYbBjBkzGDZsGD179rS6HL+zfPlyNm7cSEZGhtWl+K3vv/+eRYsWMWPGDP70pz+xfv167r33XpxOJ7feeqvV5fmNBx54gNzcXLp160ZgYCAlJSU88cQT3HzzzVaX5teys7MBiImJqbQ+JiaGPXv2WFGS3zt16hQPPvggkyZNarCLMCqg+KHp06ezefNm1q5da3Upfmffvn3cd999rFq1itDQUKvL8VulpaUMGDCApKQkAPr27cvWrVtZtGiRAkodvPbaa7z88sukpqbSo0cPMjMzSUxMJDY2lttuu83q8vyew+Go9NgwjCrr5OyKioq46aabKC0t5bnnnmuw91VA8TP33HMPK1eu5JNPPiEuLs7qcvzOhg0byMnJoX///p51JSUlfPLJJ6SkpFBQUEBgYKCFFfqHdu3aceGFF1Za1717d15//XWLKvJPM2fO5MEHH+Smm24CoFevXuzZs4fk5GQFlHpwuVyAOZLSrl07z/qcnJwqoypSs6KiIm688UaysrL46KOPGmz0BHQWj98wDIPp06fzxhtv8NFHH5GQkGB1SX5p5MiRbNmyhczMTM9twIABTJ48mczMTIWTWho6dGiV09x37txJx44dLarIP504cYKAgMq/hgMDA3WacT0lJCTgcrlIS0vzrCssLCQ9PZ0hQ4ZYWJl/cYeTb7/9lg8//JBWrVo16PtrBMVPTJs2jdTUVN566y0iIyM9x1ijo6MJCwuzuDr/ERkZWaVvJyIiglatWqmfpw5+//vfM2TIEJKSkrjxxhtZv349ixcvZvHixVaX5lfGjRvHE088QYcOHejRowebNm1i/vz53H777VaXZnvHjh1j165dnsdZWVlkZmbSsmVLOnToQGJiIklJSXTt2pWuXbuSlJREeHg4kyZNsrBqe6npM4yNjeWGG25g48aNvP3225SUlHi+d1q2bElISIjvC2yQc4Wk3oAz3l588UWrS/N7Os343PznP/8xevbsaTidTqNbt27G4sWLrS7J7+Tl5Rn33Xef0aFDByM0NNTo1KmTMXv2bKOgoMDq0mxvzZo1Z/ydeNtttxmGYZ5q/Mgjjxgul8twOp3GpZdeamzZssXaom2mps8wKyur2u+dNWvWNEh9DsMwDN/HIBEREZHaUw+KiIiI2I4CioiIiNiOAoqIiIjYjgKKiIiI2I4CioiIiNiOAoqIiIjYjgKKiIiI2I4CioiIiNiOAoqIiIjYjgKKiIiI2I4CioiIiNiOAoqIiIjYzv8HdrL6vpnkOOIAAAAASUVORK5CYII=", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plot_learning_curve(X_poly, y, Xval_poly, yval, l=0)\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["你可以看到训练的代价太低了，不真实. 这是 **过拟合**了"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# try $\\lambda=1$"]}, {"cell_type": "code", "execution_count": 49, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plot_learning_curve(X_poly, y, Xval_poly, yval, l=1)\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["\n", "训练代价增加了些，不再是0了。\n", "也就是说我们减轻**过拟合**"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# try $\\lambda=100$"]}, {"cell_type": "code", "execution_count": 50, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plot_learning_curve(X_poly, y, Xval_poly, yval, l=100)\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["太多正则化了.  \n", "变成 **欠拟合**状态"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 找到最佳的 $\\lambda$"]}, {"cell_type": "code", "execution_count": 51, "metadata": {"collapsed": true}, "outputs": [], "source": ["l_candidate = [0, 0.001, 0.003, 0.01, 0.03, 0.1, 0.3, 1, 3, 10]\n", "training_cost, cv_cost = [], []"]}, {"cell_type": "code", "execution_count": 52, "metadata": {"collapsed": true}, "outputs": [], "source": ["for l in l_candidate:\n", "    res = linear_regression_np(X_poly, y, l)\n", "    \n", "    tc = cost(res.x, X_poly, y)\n", "    cv = cost(res.x, Xval_poly, yval)\n", "    \n", "    training_cost.append(tc)\n", "    cv_cost.append(cv)"]}, {"cell_type": "code", "execution_count": 53, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plt.plot(l_candidate, training_cost, label='training')\n", "plt.plot(l_candidate, cv_cost, label='cross validation')\n", "plt.legend(loc=2)\n", "\n", "plt.xlabel('lambda')\n", "\n", "plt.ylabel('cost')\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 54, "metadata": {}, "outputs": [{"data": {"text/plain": ["1"]}, "execution_count": 54, "metadata": {}, "output_type": "execute_result"}], "source": ["# best cv I got from all those candidates\n", "l_candidate[np.argmin(cv_cost)]"]}, {"cell_type": "code", "execution_count": 55, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["test cost(l=0) = 10.22806151592189\n", "test cost(l=0.001) = 11.124691974117054\n", "test cost(l=0.003) = 11.249544975128149\n", "test cost(l=0.01) = 10.88090156583585\n", "test cost(l=0.03) = 10.021347961971756\n", "test cost(l=0.1) = 8.632042636178559\n", "test cost(l=0.3) = 7.336719115295789\n", "test cost(l=1) = 7.466293026016914\n", "test cost(l=3) = 11.64392833626852\n", "test cost(l=10) = 27.715080239043377\n"]}], "source": ["# use test data to compute the cost\n", "for l in l_candidate:\n", "    theta = linear_regression_np(X_poly, y, l).x\n", "    print('test cost(l={}) = {}'.format(l, cost(theta, Xtest_poly, ytest)))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["调参后， $\\lambda = 0.3$ 是最优选择，这个时候测试代价最小"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": true}, "outputs": [], "source": []}], "metadata": {"anaconda-cloud": {}, "kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.9"}}, "nbformat": 4, "nbformat_minor": 1}