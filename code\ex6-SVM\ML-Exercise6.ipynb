{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 机器学习练习 6 - 支持向量机"]}, {"cell_type": "markdown", "metadata": {}, "source": ["本章代码涵盖了基于Python的解决方案，用于Coursera机器学习课程的第六个编程练习。 请参考[练习文本](ex6.pdf)了解详细的说明和公式。\n", "\n", "代码修改并注释：黄海广，<EMAIL>"]}, {"cell_type": "markdown", "metadata": {}, "source": ["在本练习中，我们将使用支持向量机（SVM）来构建垃圾邮件分类器。 我们将从一些简单的2D数据集开始使用SVM来查看它们的工作原理。 然后，我们将对一组原始电子邮件进行一些预处理工作，并使用SVM在处理的电子邮件上构建分类器，以确定它们是否为垃圾邮件。"]}, {"cell_type": "markdown", "metadata": {}, "source": ["我们要做的第一件事是看一个简单的二维数据集，看看线性SVM如何对数据集进行不同的C值（类似于线性/逻辑回归中的正则化项）。 "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "import seaborn as sb\n", "from scipy.io import loadmat\n", "\n", "raw_data = loadmat('data/ex6data1.mat')\n", "raw_data"]}, {"cell_type": "markdown", "metadata": {}, "source": ["我们将其用散点图表示，其中类标签由符号表示（+表示正类，o表示负类）。"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data = pd.DataFrame(raw_data['X'], columns=['X1', 'X2'])\n", "data['y'] = raw_data['y']\n", "\n", "positive = data[data['y'].isin([1])]\n", "negative = data[data['y'].isin([0])]\n", "\n", "fig, ax = plt.subplots(figsize=(12,8))\n", "ax.scatter(positive['X1'], positive['X2'], s=50, marker='x', label='Positive')\n", "ax.scatter(negative['X1'], negative['X2'], s=50, marker='o', label='Negative')\n", "ax.legend()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["请注意，还有一个异常的正例在其他样本之外。\n", "这些类仍然是线性分离的，但它非常紧凑。 我们要训练线性支持向量机来学习类边界。 在这个练习中，我们没有从头开始执行SVM的任务，所以我要用scikit-learn。"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from sklearn import svm\n", "svc = svm.LinearSVC(C=1, loss='hinge', max_iter=1000)\n", "svc"]}, {"cell_type": "markdown", "metadata": {}, "source": ["首先，我们使用 C=1 看下结果如何。"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["svc.fit(data[['X1', 'X2']], data['y'])\n", "svc.score(data[['X1', 'X2']], data['y'])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["其次，让我们看看如果C的值越大，会发生什么"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["svc2 = svm.LinearSVC(C=100, loss='hinge', max_iter=1000)\n", "svc2.fit(data[['X1', 'X2']], data['y'])\n", "svc2.score(data[['X1', 'X2']], data['y'])"]}, {"cell_type": "markdown", "metadata": {"collapsed": true}, "source": ["这次我们得到了训练数据的完美分类，但是通过增加C的值，我们创建了一个不再适合数据的决策边界。 我们可以通过查看每个类别预测的置信水平来看出这一点，这是该点与超平面距离的函数。"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data['SVM 1 Confidence'] = svc.decision_function(data[['X1', 'X2']])\n", "\n", "fig, ax = plt.subplots(figsize=(12,8))\n", "ax.scatter(data['X1'], data['X2'], s=50, c=data['SVM 1 Confidence'], cmap='seismic')\n", "ax.set_title('SVM (C=1) Decision Confidence')\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data['SVM 2 Confidence'] = svc2.decision_function(data[['X1', 'X2']])\n", "\n", "fig, ax = plt.subplots(figsize=(12,8))\n", "ax.scatter(data['X1'], data['X2'], s=50, c=data['SVM 2 Confidence'], cmap='seismic')\n", "ax.set_title('SVM (C=100) Decision Confidence')\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["可以看看靠近边界的点的颜色，区别是有点微妙。 如果您在练习文本中，则会出现绘图，其中决策边界在图上显示为一条线，有助于使差异更清晰。\n", "\n", "现在我们将从线性SVM转移到能够使用内核进行非线性分类的SVM。 我们首先负责实现一个高斯核函数。 虽然scikit-learn具有内置的高斯内核，但为了实现更清楚，我们将从头开始实现。"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": true}, "outputs": [], "source": ["def gaussian_kernel(x1, x2, sigma):\n", "    return np.exp(-(np.sum((x1 - x2) ** 2) / (2 * (sigma ** 2))))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["x1 = np.array([1.0, 2.0, 1.0])\n", "x2 = np.array([0.0, 4.0, -1.0])\n", "sigma = 2\n", "\n", "gaussian_kernel(x1, x2, sigma)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["该结果与练习中的预期值相符。 接下来，我们将检查另一个数据集，这次用非线性决策边界。"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["raw_data = loadmat('data/ex6data2.mat')\n", "\n", "data = pd.DataFrame(raw_data['X'], columns=['X1', 'X2'])\n", "data['y'] = raw_data['y']\n", "\n", "positive = data[data['y'].isin([1])]\n", "negative = data[data['y'].isin([0])]\n", "\n", "fig, ax = plt.subplots(figsize=(12,8))\n", "ax.scatter(positive['X1'], positive['X2'], s=30, marker='x', label='Positive')\n", "ax.scatter(negative['X1'], negative['X2'], s=30, marker='o', label='Negative')\n", "ax.legend()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["对于该数据集，我们将使用内置的RBF内核构建支持向量机分类器，并检查其对训练数据的准确性。 为了可视化决策边界，这一次我们将根据实例具有负类标签的预测概率来对点做阴影。 从结果可以看出，它们大部分是正确的。"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["svc = svm.SVC(C=100, gamma=10, probability=True)\n", "svc"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["svc.fit(data[['X1', 'X2']], data['y'])\n", "svc.score(data[['X1', 'X2']], data['y'])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data['Probability'] = svc.predict_proba(data[['X1', 'X2']])[:,0]\n", "\n", "fig, ax = plt.subplots(figsize=(12,8))\n", "ax.scatter(data['X1'], data['X2'], s=30, c=data['Probability'], cmap='Reds')\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["对于第三个数据集，我们给出了训练和验证集，并且基于验证集性能为SVM模型找到最优超参数。 虽然我们可以使用scikit-learn的内置网格搜索来做到这一点，但是本着遵循练习的目的，我们将从头开始实现一个简单的网格搜索。"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["raw_data = loadmat('data/ex6data3.mat')\n", "\n", "X = raw_data['X']\n", "Xval = raw_data['Xval']\n", "y = raw_data['y'].ravel()\n", "yval = raw_data['yval'].ravel()\n", "\n", "C_values = [0.01, 0.03, 0.1, 0.3, 1, 3, 10, 30, 100]\n", "gamma_values = [0.01, 0.03, 0.1, 0.3, 1, 3, 10, 30, 100]\n", "\n", "best_score = 0\n", "best_params = {'C': None, 'gamma': None}\n", "\n", "for C in C_values:\n", "    for gamma in gamma_values:\n", "        svc = svm.SVC(C=C, gamma=gamma)\n", "        svc.fit(X, y)\n", "        score = svc.score(Xval, yval)\n", "        \n", "        if score > best_score:\n", "            best_score = score\n", "            best_params['C'] = C\n", "            best_params['gamma'] = gamma\n", "\n", "best_score, best_params"]}, {"cell_type": "markdown", "metadata": {}, "source": ["现在，我们将进行第二部分的练习。 在这一部分中，我们的目标是使用SVM来构建垃圾邮件过滤器。 在练习文本中，有一个任务涉及一些文本预处理，以获得适合SVM处理的格式的数据。 然而，这个任务很简单（将字词映射到为练习提供的字典中的ID），而其余的预处理步骤（如HTML删除，词干，标准化等）已经完成。 我将跳过机器学习任务，而不是重现这些预处理步骤，其中包括从预处理过的训练集构建分类器，以及将垃圾邮件和非垃圾邮件转换为单词出现次数的向量的测试数据集。"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["spam_train = loadmat('data/spamTrain.mat')\n", "spam_test = loadmat('data/spamTest.mat')\n", "\n", "spam_train"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["X = spam_train['X']\n", "Xtest = spam_test['Xtest']\n", "y = spam_train['y'].ravel()\n", "ytest = spam_test['ytest'].ravel()\n", "\n", "X.shape, y.shape, Xtest.shape, ytest.shape"]}, {"cell_type": "markdown", "metadata": {}, "source": ["每个文档已经转换为一个向量，其中1,899个维对应于词汇表中的1,899个单词。 它们的值为二进制，表示文档中是否存在单词。 在这一点上，训练评估是用一个分类器拟合测试数据的问题。"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["svc = svm.SVC()\n", "svc.fit(X, y)\n", "print('Training accuracy = {0}%'.format(np.round(svc.score(X, y) * 100, 2)))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print('Test accuracy = {0}%'.format(np.round(svc.score(Xtest, ytest) * 100, 2)))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["这个结果是使用使用默认参数的。 我们可能会使用一些参数调整来获得更高的精度，尽管95％的精度相当不错。\n", "\n", "结束练习6！ 在下一个练习中，我们将使用K-Means和主成分分析进行聚类和图像压缩。"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": true}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.9"}}, "nbformat": 4, "nbformat_minor": 1}