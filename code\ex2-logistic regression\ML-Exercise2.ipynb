{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 机器学习练习 2 - 逻辑回归"]}, {"cell_type": "markdown", "metadata": {}, "source": ["这个笔记包含了以Python为编程语言的Coursera上机器学习的第二次编程练习。请参考 [作业文件](ex2.pdf) 详细描述和方程。\n", "在这一次练习中，我们将要实现逻辑回归并且应用到一个分类任务。我们还将通过将正则化加入训练算法，来提高算法的鲁棒性，并用更复杂的情形来测试它。\n", "\n", "代码修改并注释：黄海广，<EMAIL>"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 逻辑回归"]}, {"cell_type": "markdown", "metadata": {}, "source": ["在训练的初始阶段，我们将要构建一个逻辑回归模型来预测，某个学生是否被大学录取。设想你是大学相关部分的管理者，想通过申请学生两次测试的评分，来决定他们是否被录取。现在你拥有之前申请学生的可以用于训练逻辑回归的训练样本集。对于每一个训练样本，你有他们两次测试的评分和最后是被录取的结果。为了完成这个预测任务，我们准备构建一个可以基于两次测试评分来评估录取可能性的分类模型。"]}, {"cell_type": "markdown", "metadata": {}, "source": ["让我们从检查数据开始。"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import pandas as pd\n", "import matplotlib.pyplot as plt"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Exam 1</th>\n", "      <th>Exam 2</th>\n", "      <th>Admitted</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>34.623660</td>\n", "      <td>78.024693</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>30.286711</td>\n", "      <td>43.894998</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>35.847409</td>\n", "      <td>72.902198</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>60.182599</td>\n", "      <td>86.308552</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>79.032736</td>\n", "      <td>75.344376</td>\n", "      <td>1</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["      Exam 1     Exam 2  Admitted\n", "0  34.623660  78.024693         0\n", "1  30.286711  43.894998         0\n", "2  35.847409  72.902198         0\n", "3  60.182599  86.308552         1\n", "4  79.032736  75.344376         1"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["path = 'ex2data1.txt'\n", "data = pd.read_csv(path, header=None, names=['Exam 1', 'Exam 2', 'Admitted'])\n", "data.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["让我们创建两个分数的散点图，并使用颜色编码来可视化，如果样本是正的（被接纳）或负的（未被接纳）。"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1200x800 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["positive = data[data['Admitted'].isin([1])]\n", "negative = data[data['Admitted'].isin([0])]\n", "\n", "fig, ax = plt.subplots(figsize=(12,8))\n", "ax.scatter(positive['Exam 1'], positive['Exam 2'], s=50, c='b', marker='o', label='Admitted')\n", "ax.scatter(negative['Exam 1'], negative['Exam 2'], s=50, c='r', marker='x', label='Not Admitted')\n", "ax.legend()\n", "ax.set_xlabel('Exam 1 Score')\n", "ax.set_ylabel('Exam 2 Score')\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["看起来在两类间，有一个清晰的决策边界。现在我们需要实现逻辑回归，那样就可以训练一个模型来预测结果。方程实现在下面的代码示例在\"exercises\" 文件夹的 \"ex2.pdf\" 中。"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# sigmoid 函数\n", "g 代表一个常用的逻辑函数（logistic function）为S形函数（Sigmoid function），公式为： \\\\[g\\left( z \\right)=\\frac{1}{1+{{e}^{-z}}}\\\\] \n", "合起来，我们得到逻辑回归模型的假设函数： \n", "\t\\\\[{{h}_{\\theta }}\\left( x \\right)=\\frac{1}{1+{{e}^{-{{\\theta }^{T}}X}}}\\\\] "]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["def sigmoid(z):\n", "    return 1 / (1 + np.exp(-z))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["让我们做一个快速的检查，来确保它可以工作。"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1200x800 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["nums = np.arange(-10, 10, step=1)\n", "\n", "fig, ax = plt.subplots(figsize=(12,8))\n", "ax.plot(nums, sigmoid(nums), 'r')\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["棒极了！现在，我们需要编写代价函数来评估结果。\n", "代价函数：\n", "$J\\left( \\theta  \\right)=\\frac{1}{m}\\sum\\limits_{i=1}^{m}{[-{{y}^{(i)}}\\log \\left( {{h}_{\\theta }}\\left( {{x}^{(i)}} \\right) \\right)-\\left( 1-{{y}^{(i)}} \\right)\\log \\left( 1-{{h}_{\\theta }}\\left( {{x}^{(i)}} \\right) \\right)]}$"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["def cost(theta, X, y):\n", "    theta = np.matrix(theta)\n", "    X = np.matrix(X)\n", "    y = np.matrix(y)\n", "    first = np.multiply(-y, np.log(sigmoid(X * theta.T)))\n", "    second = np.multiply((1 - y), np.log(1 - sigmoid(X * theta.T)))\n", "    return np.sum(first - second) / (len(X))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["现在，我们要做一些设置，和我们在练习1在线性回归的练习很相似。"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["# add a ones column - this makes the matrix multiplication work out easier\n", "data.insert(0, 'Ones', 1)\n", "\n", "# set X (training data) and y (target variable)\n", "cols = data.shape[1]\n", "X = data.iloc[:,0:cols-1]\n", "y = data.iloc[:,cols-1:cols]\n", "\n", "# convert to numpy arrays and initalize the parameter array theta\n", "X = np.array(X.values)\n", "y = np.array(y.values)\n", "theta = np.zeros(3)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["让我们来检查矩阵的维度来确保一切良好。"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([0., 0., 0.])"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["theta"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"data": {"text/plain": ["((100, 3), (3,), (100, 1))"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["X.shape, theta.shape, y.shape"]}, {"cell_type": "markdown", "metadata": {}, "source": ["让我们计算初始化参数的代价函数(theta为0)。"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"data": {"text/plain": ["0.6931471805599453"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["cost(theta, X, y)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["看起来不错，接下来，我们需要一个函数来计算我们的训练数据、标签和一些参数thata的梯度。"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# gradient descent(梯度下降)\n", "* 这是批量梯度下降（batch gradient descent）  \n", "* 转化为向量化计算： $\\frac{1}{m} X^T( Sigmoid(X\\theta) - y )$\n", "$$\\frac{\\partial J\\left( \\theta  \\right)}{\\partial {{\\theta }_{j}}}=\\frac{1}{m}\\sum\\limits_{i=1}^{m}{({{h}_{\\theta }}\\left( {{x}^{(i)}} \\right)-{{y}^{(i)}})x_{_{j}}^{(i)}}$$"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [], "source": ["def gradient(theta, X, y):\n", "    theta = np.matrix(theta)\n", "    X = np.matrix(X)\n", "    y = np.matrix(y)\n", "    \n", "    parameters = int(theta.ravel().shape[1])\n", "    grad = np.zeros(parameters)\n", "    \n", "    error = sigmoid(X * theta.T) - y\n", "    \n", "    for i in range(parameters):\n", "        term = np.multiply(error, X[:,i])\n", "        grad[i] = np.sum(term) / len(X)\n", "    \n", "    return grad"]}, {"cell_type": "markdown", "metadata": {}, "source": ["注意，我们实际上没有在这个函数中执行梯度下降，我们仅仅在计算一个梯度步长。在练习中，一个称为“fminunc”的Octave函数是用来优化函数来计算成本和梯度参数。由于我们使用Python，我们可以用SciPy的“optimize”命名空间来做同样的事情。"]}, {"cell_type": "markdown", "metadata": {}, "source": ["我们看看用我们的数据和初始参数为0的梯度下降法的结果。"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([ -0.1       , -12.00921659, -11.26284221])"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["gradient(theta, X, y)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["现在可以用SciPy's truncated newton（TNC）实现寻找最优参数。"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"data": {"text/plain": ["(array([-25.16131866,   0.20623159,   0.20147149]), 36, 0)"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["import scipy.optimize as opt\n", "result = opt.fmin_tnc(func=cost, x0=theta, fprime=gradient, args=(X, y))\n", "result"]}, {"cell_type": "markdown", "metadata": {}, "source": ["让我们看看在这个结论下代价函数计算结果是什么个样子~"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"data": {"text/plain": ["0.20349770158947447"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["cost(result[0], X, y)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["接下来，我们需要编写一个函数，用我们所学的参数theta来为数据集X输出预测。然后，我们可以使用这个函数来给我们的分类器的训练精度打分。\n", "逻辑回归模型的假设函数： \n", "\t\\\\[{{h}_{\\theta }}\\left( x \\right)=\\frac{1}{1+{{e}^{-{{\\theta }^{T}}X}}}\\\\] \n", "当${{h}_{\\theta }}$大于等于0.5时，预测 y=1\n", "\n", "当${{h}_{\\theta }}$小于0.5时，预测 y=0 。"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [], "source": ["def predict(theta, X):\n", "    probability = sigmoid(X * theta.T)\n", "    return [1 if x >= 0.5 else 0 for x in probability]"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["accuracy = 89%\n"]}], "source": ["theta_min = np.matrix(result[0])\n", "predictions = predict(theta_min, X)\n", "correct = [1 if ((a == 1 and b == 1) or (a == 0 and b == 0)) else 0 for (a, b) in zip(predictions, y)]\n", "accuracy = (sum(map(int, correct)) % len(correct))\n", "print ('accuracy = {0}%'.format(accuracy))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["我们的逻辑回归分类器预测正确，如果一个学生被录取或没有录取，达到89%的精确度。不坏！记住，这是训练集的准确性。我们没有保持住了设置或使用交叉验证得到的真实逼近，所以这个数字有可能高于其真实值（这个话题将在以后说明）。"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 正则化逻辑回归"]}, {"cell_type": "markdown", "metadata": {}, "source": ["在训练的第二部分，我们将要通过加入正则项提升逻辑回归算法。如果你对正则化有点眼生，或者喜欢这一节的方程的背景，请参考在\"exercises\"文件夹中的\"ex2.pdf\"。简而言之，正则化是成本函数中的一个术语，它使算法更倾向于“更简单”的模型（在这种情况下，模型将更小的系数）。这个理论助于减少过拟合，提高模型的泛化能力。这样，我们开始吧。"]}, {"cell_type": "markdown", "metadata": {}, "source": ["设想你是工厂的生产主管，你有一些芯片在两次测试中的测试结果。对于这两次测试，你想决定是否芯片要被接受或抛弃。为了帮助你做出艰难的决定，你拥有过去芯片的测试数据集，从其中你可以构建一个逻辑回归模型。"]}, {"cell_type": "markdown", "metadata": {}, "source": ["和第一部分很像，从数据可视化开始吧！"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Test 1</th>\n", "      <th>Test 2</th>\n", "      <th>Accepted</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>0.051267</td>\n", "      <td>0.69956</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>-0.092742</td>\n", "      <td>0.68494</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>-0.213710</td>\n", "      <td>0.69225</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>-0.375000</td>\n", "      <td>0.50219</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>-0.513250</td>\n", "      <td>0.46564</td>\n", "      <td>1</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["     Test 1   Test 2  Accepted\n", "0  0.051267  0.69956         1\n", "1 -0.092742  0.68494         1\n", "2 -0.213710  0.69225         1\n", "3 -0.375000  0.50219         1\n", "4 -0.513250  0.46564         1"]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["path =  'ex2data2.txt'\n", "data2 = pd.read_csv(path, header=None, names=['Test 1', 'Test 2', 'Accepted'])\n", "data2.head()"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"data": {"image/png": "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**************************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", "text/plain": ["<Figure size 1200x800 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["positive = data2[data2['Accepted'].isin([1])]\n", "negative = data2[data2['Accepted'].isin([0])]\n", "\n", "fig, ax = plt.subplots(figsize=(12,8))\n", "ax.scatter(positive['Test 1'], positive['Test 2'], s=50, c='b', marker='o', label='Accepted')\n", "ax.scatter(negative['Test 1'], negative['Test 2'], s=50, c='r', marker='x', label='Rejected')\n", "ax.legend()\n", "ax.set_xlabel('Test 1 Score')\n", "ax.set_ylabel('Test 2 Score')\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["哇，这个数据看起来可比前一次的复杂得多。特别地，你会注意到其中没有线性决策界限，来良好的分开两类数据。一个方法是用像逻辑回归这样的线性技术来构造从原始特征的多项式中得到的特征。让我们通过创建一组多项式特征入手吧。"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Accepted</th>\n", "      <th>Ones</th>\n", "      <th>F10</th>\n", "      <th>F20</th>\n", "      <th>F21</th>\n", "      <th>F30</th>\n", "      <th>F31</th>\n", "      <th>F32</th>\n", "      <th>F40</th>\n", "      <th>F41</th>\n", "      <th>F42</th>\n", "      <th>F43</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>0.051267</td>\n", "      <td>0.002628</td>\n", "      <td>0.035864</td>\n", "      <td>0.000135</td>\n", "      <td>0.001839</td>\n", "      <td>0.025089</td>\n", "      <td>0.000007</td>\n", "      <td>0.000094</td>\n", "      <td>0.001286</td>\n", "      <td>0.017551</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>-0.092742</td>\n", "      <td>0.008601</td>\n", "      <td>-0.063523</td>\n", "      <td>-0.000798</td>\n", "      <td>0.005891</td>\n", "      <td>-0.043509</td>\n", "      <td>0.000074</td>\n", "      <td>-0.000546</td>\n", "      <td>0.004035</td>\n", "      <td>-0.029801</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>-0.213710</td>\n", "      <td>0.045672</td>\n", "      <td>-0.147941</td>\n", "      <td>-0.009761</td>\n", "      <td>0.031616</td>\n", "      <td>-0.102412</td>\n", "      <td>0.002086</td>\n", "      <td>-0.006757</td>\n", "      <td>0.021886</td>\n", "      <td>-0.070895</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>-0.375000</td>\n", "      <td>0.140625</td>\n", "      <td>-0.188321</td>\n", "      <td>-0.052734</td>\n", "      <td>0.070620</td>\n", "      <td>-0.094573</td>\n", "      <td>0.019775</td>\n", "      <td>-0.026483</td>\n", "      <td>0.035465</td>\n", "      <td>-0.047494</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>-0.513250</td>\n", "      <td>0.263426</td>\n", "      <td>-0.238990</td>\n", "      <td>-0.135203</td>\n", "      <td>0.122661</td>\n", "      <td>-0.111283</td>\n", "      <td>0.069393</td>\n", "      <td>-0.062956</td>\n", "      <td>0.057116</td>\n", "      <td>-0.051818</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   Accepted  Ones       F10       F20       F21       F30       F31       F32  \\\n", "0         1     1  0.051267  0.002628  0.035864  0.000135  0.001839  0.025089   \n", "1         1     1 -0.092742  0.008601 -0.063523 -0.000798  0.005891 -0.043509   \n", "2         1     1 -0.213710  0.045672 -0.147941 -0.009761  0.031616 -0.102412   \n", "3         1     1 -0.375000  0.140625 -0.188321 -0.052734  0.070620 -0.094573   \n", "4         1     1 -0.513250  0.263426 -0.238990 -0.135203  0.122661 -0.111283   \n", "\n", "        F40       F41       F42       F43  \n", "0  0.000007  0.000094  0.001286  0.017551  \n", "1  0.000074 -0.000546  0.004035 -0.029801  \n", "2  0.002086 -0.006757  0.021886 -0.070895  \n", "3  0.019775 -0.026483  0.035465 -0.047494  \n", "4  0.069393 -0.062956  0.057116 -0.051818  "]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}], "source": ["degree = 5\n", "x1 = data2['Test 1']\n", "x2 = data2['Test 2']\n", "\n", "data2.insert(3, 'Ones', 1)\n", "\n", "for i in range(1, degree):\n", "    for j in range(0, i):\n", "        data2['F' + str(i) + str(j)] = np.power(x1, i-j) * np.power(x2, j)\n", "\n", "data2.drop('Test 1', axis=1, inplace=True)\n", "data2.drop('Test 2', axis=1, inplace=True)\n", "\n", "data2.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["现在，我们需要修改第1部分的成本和梯度函数，包括正则化项。首先是成本函数："]}, {"cell_type": "markdown", "metadata": {}, "source": ["# regularized cost（正则化代价函数）\n", "$$J\\left( \\theta  \\right)=\\frac{1}{m}\\sum\\limits_{i=1}^{m}{[-{{y}^{(i)}}\\log \\left( {{h}_{\\theta }}\\left( {{x}^{(i)}} \\right) \\right)-\\left( 1-{{y}^{(i)}} \\right)\\log \\left( 1-{{h}_{\\theta }}\\left( {{x}^{(i)}} \\right) \\right)]}+\\frac{\\lambda }{2m}\\sum\\limits_{j=1}^{n}{\\theta _{j}^{2}}$$"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [], "source": ["def costReg(theta, X, y, learningRate):\n", "    theta = np.matrix(theta)\n", "    X = np.matrix(X)\n", "    y = np.matrix(y)\n", "    first = np.multiply(-y, np.log(sigmoid(X * theta.T)))\n", "    second = np.multiply((1 - y), np.log(1 - sigmoid(X * theta.T)))\n", "    reg = (learningRate / (2 * len(X))) * np.sum(np.power(theta[:,1:theta.shape[1]], 2))\n", "    return np.sum(first - second) / len(X) + reg"]}, {"cell_type": "markdown", "metadata": {}, "source": ["请注意等式中的\"reg\" 项。还注意到另外的一个“学习率”参数。这是一种超参数，用来控制正则化项。现在我们需要添加正则化梯度函数："]}, {"cell_type": "markdown", "metadata": {}, "source": ["如果我们要使用梯度下降法令这个代价函数最小化，因为我们未对${{\\theta }_{0}}$ 进行正则化，所以梯度下降算法将分两种情形：\n", "\\begin{align}\n", "  & Repeat\\text{ }until\\text{ }convergence\\text{ }\\!\\!\\{\\!\\!\\text{ } \\\\ \n", " & \\text{     }{{\\theta }_{0}}:={{\\theta }_{0}}-a\\frac{1}{m}\\sum\\limits_{i=1}^{m}{[{{h}_{\\theta }}\\left( {{x}^{(i)}} \\right)-{{y}^{(i)}}]x_{_{0}}^{(i)}} \\\\ \n", " & \\text{     }{{\\theta }_{j}}:={{\\theta }_{j}}-a\\frac{1}{m}\\sum\\limits_{i=1}^{m}{[{{h}_{\\theta }}\\left( {{x}^{(i)}} \\right)-{{y}^{(i)}}]x_{j}^{(i)}}+\\frac{\\lambda }{m}{{\\theta }_{j}} \\\\ \n", " & \\text{          }\\!\\!\\}\\!\\!\\text{ } \\\\ \n", " & Repeat \\\\ \n", "\\end{align}\n", "\n", "对上面的算法中 j=1,2,...,n 时的更新式子进行调整可得： \n", "${{\\theta }_{j}}:={{\\theta }_{j}}(1-a\\frac{\\lambda }{m})-a\\frac{1}{m}\\sum\\limits_{i=1}^{m}{({{h}_{\\theta }}\\left( {{x}^{(i)}} \\right)-{{y}^{(i)}})x_{j}^{(i)}}$\n"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [], "source": ["def gradientReg(theta, X, y, learningRate):\n", "    theta = np.matrix(theta)\n", "    X = np.matrix(X)\n", "    y = np.matrix(y)\n", "    \n", "    parameters = int(theta.ravel().shape[1])\n", "    grad = np.zeros(parameters)\n", "    \n", "    error = sigmoid(X * theta.T) - y\n", "    \n", "    for i in range(parameters):\n", "        term = np.multiply(error, X[:,i])\n", "        \n", "        if (i == 0):\n", "            grad[i] = np.sum(term) / len(X)\n", "        else:\n", "            grad[i] = (np.sum(term) / len(X)) + ((learningRate / len(X)) * theta[:,i])\n", "    \n", "    return grad"]}, {"cell_type": "markdown", "metadata": {}, "source": ["就像在第一部分中做的一样，初始化变量。"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [], "source": ["# set X and y (remember from above that we moved the label to column 0)\n", "cols = data2.shape[1]\n", "X2 = data2.iloc[:,1:cols]\n", "y2 = data2.iloc[:,0:1]\n", "\n", "# convert to numpy arrays and initalize the parameter array theta\n", "X2 = np.array(X2.values)\n", "y2 = np.array(y2.values)\n", "theta2 = np.zeros(11)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["让我们初始学习率到一个合理值。，果有必要的话（即如果惩罚太强或不够强）,我们可以之后再折腾这个。"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [], "source": ["learningRate = 1"]}, {"cell_type": "markdown", "metadata": {}, "source": ["现在，让我们尝试调用新的默认为0的theta的正则化函数，以确保计算工作正常。"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [{"data": {"text/plain": ["0.6931471805599454"]}, "execution_count": 24, "metadata": {}, "output_type": "execute_result"}], "source": ["costReg(theta2, X2, y2, learningRate)"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Windows\\Temp\\ipykernel_6444\\424655318.py:17: DeprecationWarning: Conversion of an array with ndim > 0 to a scalar is deprecated, and will error in future. Ensure you extract a single element from your array before performing this operation. (Deprecated NumPy 1.25.)\n", "  grad[i] = (np.sum(term) / len(X)) + ((learningRate / len(X)) * theta[:,i])\n"]}, {"data": {"text/plain": ["array([0.00847458, 0.01878809, 0.05034464, 0.01150133, 0.01835599,\n", "       0.00732393, 0.00819244, 0.03934862, 0.00223924, 0.01286005,\n", "       0.00309594])"]}, "execution_count": 25, "metadata": {}, "output_type": "execute_result"}], "source": ["gradientReg(theta2, X2, y2, learningRate)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["现在我们可以使用和第一部分相同的优化函数来计算优化后的结果。"]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Windows\\Temp\\ipykernel_6444\\424655318.py:17: DeprecationWarning: Conversion of an array with ndim > 0 to a scalar is deprecated, and will error in future. Ensure you extract a single element from your array before performing this operation. (Deprecated NumPy 1.25.)\n", "  grad[i] = (np.sum(term) / len(X)) + ((learningRate / len(X)) * theta[:,i])\n"]}, {"data": {"text/plain": ["(array([ 0.53010247,  0.29075567, -1.60725764, -0.58213819,  0.01781027,\n", "        -0.21329507, -0.40024142, -1.3714414 ,  0.02264304, -0.95033581,\n", "         0.0344085 ]),\n", " 22,\n", " 1)"]}, "execution_count": 26, "metadata": {}, "output_type": "execute_result"}], "source": ["result2 = opt.fmin_tnc(func=costReg, x0=theta2, fprime=gradientReg, args=(X2, y2, learningRate))\n", "result2"]}, {"cell_type": "markdown", "metadata": {}, "source": ["最后，我们可以使用第1部分中的预测函数来查看我们的方案在训练数据上的准确度。"]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["accuracy = 0.6610169491525424%\n"]}], "source": ["theta_min = np.matrix(result2[0])\n", "predictions = predict(theta_min, X2)\n", "correct = [1 if ((a == 1 and b == 1) or (a == 0 and b == 0)) else 0 for (a, b) in zip(predictions, y2)]\n", "accuracy = (sum(map(int, correct)) / len(correct))\n", "print ('accuracy = {0}%'.format(accuracy))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["虽然我们实现了这些算法，值得注意的是，我们还可以使用高级Python库像scikit-learn来解决这个问题。"]}, {"cell_type": "code", "execution_count": 28, "metadata": {}, "outputs": [{"data": {"text/html": ["<style>#sk-container-id-1 {\n", "  /* Definition of color scheme common for light and dark mode */\n", "  --sklearn-color-text: black;\n", "  --sklearn-color-line: gray;\n", "  /* Definition of color scheme for unfitted estimators */\n", "  --sklearn-color-unfitted-level-0: #fff5e6;\n", "  --sklearn-color-unfitted-level-1: #f6e4d2;\n", "  --sklearn-color-unfitted-level-2: #ffe0b3;\n", "  --sklearn-color-unfitted-level-3: chocolate;\n", "  /* Definition of color scheme for fitted estimators */\n", "  --sklearn-color-fitted-level-0: #f0f8ff;\n", "  --sklearn-color-fitted-level-1: #d4ebff;\n", "  --sklearn-color-fitted-level-2: #b3dbfd;\n", "  --sklearn-color-fitted-level-3: cornflowerblue;\n", "\n", "  /* Specific color for light theme */\n", "  --sklearn-color-text-on-default-background: var(--sg-text-color, var(--theme-code-foreground, var(--jp-content-font-color1, black)));\n", "  --sklearn-color-background: var(--sg-background-color, var(--theme-background, var(--jp-layout-color0, white)));\n", "  --sklearn-color-border-box: var(--sg-text-color, var(--theme-code-foreground, var(--jp-content-font-color1, black)));\n", "  --sklearn-color-icon: #696969;\n", "\n", "  @media (prefers-color-scheme: dark) {\n", "    /* Redefinition of color scheme for dark theme */\n", "    --sklearn-color-text-on-default-background: var(--sg-text-color, var(--theme-code-foreground, var(--jp-content-font-color1, white)));\n", "    --sklearn-color-background: var(--sg-background-color, var(--theme-background, var(--jp-layout-color0, #111)));\n", "    --sklearn-color-border-box: var(--sg-text-color, var(--theme-code-foreground, var(--jp-content-font-color1, white)));\n", "    --sklearn-color-icon: #878787;\n", "  }\n", "}\n", "\n", "#sk-container-id-1 {\n", "  color: var(--sklearn-color-text);\n", "}\n", "\n", "#sk-container-id-1 pre {\n", "  padding: 0;\n", "}\n", "\n", "#sk-container-id-1 input.sk-hidden--visually {\n", "  border: 0;\n", "  clip: rect(1px 1px 1px 1px);\n", "  clip: rect(1px, 1px, 1px, 1px);\n", "  height: 1px;\n", "  margin: -1px;\n", "  overflow: hidden;\n", "  padding: 0;\n", "  position: absolute;\n", "  width: 1px;\n", "}\n", "\n", "#sk-container-id-1 div.sk-dashed-wrapped {\n", "  border: 1px dashed var(--sklearn-color-line);\n", "  margin: 0 0.4em 0.5em 0.4em;\n", "  box-sizing: border-box;\n", "  padding-bottom: 0.4em;\n", "  background-color: var(--sklearn-color-background);\n", "}\n", "\n", "#sk-container-id-1 div.sk-container {\n", "  /* jup<PERSON>r's `normalize.less` sets `[hidden] { display: none; }`\n", "     but bootstrap.min.css set `[hidden] { display: none !important; }`\n", "     so we also need the `!important` here to be able to override the\n", "     default hidden behavior on the sphinx rendered scikit-learn.org.\n", "     See: https://github.com/scikit-learn/scikit-learn/issues/21755 */\n", "  display: inline-block !important;\n", "  position: relative;\n", "}\n", "\n", "#sk-container-id-1 div.sk-text-repr-fallback {\n", "  display: none;\n", "}\n", "\n", "div.sk-parallel-item,\n", "div.sk-serial,\n", "div.sk-item {\n", "  /* draw centered vertical line to link estimators */\n", "  background-image: linear-gradient(var(--sklearn-color-text-on-default-background), var(--sklearn-color-text-on-default-background));\n", "  background-size: 2px 100%;\n", "  background-repeat: no-repeat;\n", "  background-position: center center;\n", "}\n", "\n", "/* Parallel-specific style estimator block */\n", "\n", "#sk-container-id-1 div.sk-parallel-item::after {\n", "  content: \"\";\n", "  width: 100%;\n", "  border-bottom: 2px solid var(--sklearn-color-text-on-default-background);\n", "  flex-grow: 1;\n", "}\n", "\n", "#sk-container-id-1 div.sk-parallel {\n", "  display: flex;\n", "  align-items: stretch;\n", "  justify-content: center;\n", "  background-color: var(--sklearn-color-background);\n", "  position: relative;\n", "}\n", "\n", "#sk-container-id-1 div.sk-parallel-item {\n", "  display: flex;\n", "  flex-direction: column;\n", "}\n", "\n", "#sk-container-id-1 div.sk-parallel-item:first-child::after {\n", "  align-self: flex-end;\n", "  width: 50%;\n", "}\n", "\n", "#sk-container-id-1 div.sk-parallel-item:last-child::after {\n", "  align-self: flex-start;\n", "  width: 50%;\n", "}\n", "\n", "#sk-container-id-1 div.sk-parallel-item:only-child::after {\n", "  width: 0;\n", "}\n", "\n", "/* Serial-specific style estimator block */\n", "\n", "#sk-container-id-1 div.sk-serial {\n", "  display: flex;\n", "  flex-direction: column;\n", "  align-items: center;\n", "  background-color: var(--sklearn-color-background);\n", "  padding-right: 1em;\n", "  padding-left: 1em;\n", "}\n", "\n", "\n", "/* Toggleable style: style used for estimator/Pipeline/ColumnTransformer box that is\n", "clickable and can be expanded/collapsed.\n", "- Pipeline and ColumnTransformer use this feature and define the default style\n", "- Estimators will overwrite some part of the style using the `sk-estimator` class\n", "*/\n", "\n", "/* Pipeline and ColumnTransformer style (default) */\n", "\n", "#sk-container-id-1 div.sk-toggleable {\n", "  /* Default theme specific background. It is overwritten whether we have a\n", "  specific estimator or a Pipeline/ColumnTransformer */\n", "  background-color: var(--sklearn-color-background);\n", "}\n", "\n", "/* Toggleable label */\n", "#sk-container-id-1 label.sk-toggleable__label {\n", "  cursor: pointer;\n", "  display: block;\n", "  width: 100%;\n", "  margin-bottom: 0;\n", "  padding: 0.5em;\n", "  box-sizing: border-box;\n", "  text-align: center;\n", "}\n", "\n", "#sk-container-id-1 label.sk-toggleable__label-arrow:before {\n", "  /* <PERSON> on the left of the label */\n", "  content: \"▸\";\n", "  float: left;\n", "  margin-right: 0.25em;\n", "  color: var(--sklearn-color-icon);\n", "}\n", "\n", "#sk-container-id-1 label.sk-toggleable__label-arrow:hover:before {\n", "  color: var(--sklearn-color-text);\n", "}\n", "\n", "/* Toggleable content - dropdown */\n", "\n", "#sk-container-id-1 div.sk-toggleable__content {\n", "  max-height: 0;\n", "  max-width: 0;\n", "  overflow: hidden;\n", "  text-align: left;\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-0);\n", "}\n", "\n", "#sk-container-id-1 div.sk-toggleable__content.fitted {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-0);\n", "}\n", "\n", "#sk-container-id-1 div.sk-toggleable__content pre {\n", "  margin: 0.2em;\n", "  border-radius: 0.25em;\n", "  color: var(--sklearn-color-text);\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-0);\n", "}\n", "\n", "#sk-container-id-1 div.sk-toggleable__content.fitted pre {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-fitted-level-0);\n", "}\n", "\n", "#sk-container-id-1 input.sk-toggleable__control:checked~div.sk-toggleable__content {\n", "  /* Expand drop-down */\n", "  max-height: 200px;\n", "  max-width: 100%;\n", "  overflow: auto;\n", "}\n", "\n", "#sk-container-id-1 input.sk-toggleable__control:checked~label.sk-toggleable__label-arrow:before {\n", "  content: \"▾\";\n", "}\n", "\n", "/* Pipeline/ColumnTransformer-specific style */\n", "\n", "#sk-container-id-1 div.sk-label input.sk-toggleable__control:checked~label.sk-toggleable__label {\n", "  color: var(--sklearn-color-text);\n", "  background-color: var(--sklearn-color-unfitted-level-2);\n", "}\n", "\n", "#sk-container-id-1 div.sk-label.fitted input.sk-toggleable__control:checked~label.sk-toggleable__label {\n", "  background-color: var(--sklearn-color-fitted-level-2);\n", "}\n", "\n", "/* Estimator-specific style */\n", "\n", "/* Colorize estimator box */\n", "#sk-container-id-1 div.sk-estimator input.sk-toggleable__control:checked~label.sk-toggleable__label {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-2);\n", "}\n", "\n", "#sk-container-id-1 div.sk-estimator.fitted input.sk-toggleable__control:checked~label.sk-toggleable__label {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-2);\n", "}\n", "\n", "#sk-container-id-1 div.sk-label label.sk-toggleable__label,\n", "#sk-container-id-1 div.sk-label label {\n", "  /* The background is the default theme color */\n", "  color: var(--sklearn-color-text-on-default-background);\n", "}\n", "\n", "/* On hover, darken the color of the background */\n", "#sk-container-id-1 div.sk-label:hover label.sk-toggleable__label {\n", "  color: var(--sklearn-color-text);\n", "  background-color: var(--sklearn-color-unfitted-level-2);\n", "}\n", "\n", "/* Label box, darken color on hover, fitted */\n", "#sk-container-id-1 div.sk-label.fitted:hover label.sk-toggleable__label.fitted {\n", "  color: var(--sklearn-color-text);\n", "  background-color: var(--sklearn-color-fitted-level-2);\n", "}\n", "\n", "/* Estimator label */\n", "\n", "#sk-container-id-1 div.sk-label label {\n", "  font-family: monospace;\n", "  font-weight: bold;\n", "  display: inline-block;\n", "  line-height: 1.2em;\n", "}\n", "\n", "#sk-container-id-1 div.sk-label-container {\n", "  text-align: center;\n", "}\n", "\n", "/* Estimator-specific */\n", "#sk-container-id-1 div.sk-estimator {\n", "  font-family: monospace;\n", "  border: 1px dotted var(--sklearn-color-border-box);\n", "  border-radius: 0.25em;\n", "  box-sizing: border-box;\n", "  margin-bottom: 0.5em;\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-0);\n", "}\n", "\n", "#sk-container-id-1 div.sk-estimator.fitted {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-0);\n", "}\n", "\n", "/* on hover */\n", "#sk-container-id-1 div.sk-estimator:hover {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-2);\n", "}\n", "\n", "#sk-container-id-1 div.sk-estimator.fitted:hover {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-2);\n", "}\n", "\n", "/* Specification for estimator info (e.g. \"i\" and \"?\") */\n", "\n", "/* Common style for \"i\" and \"?\" */\n", "\n", ".sk-estimator-doc-link,\n", "a:link.sk-estimator-doc-link,\n", "a:visited.sk-estimator-doc-link {\n", "  float: right;\n", "  font-size: smaller;\n", "  line-height: 1em;\n", "  font-family: monospace;\n", "  background-color: var(--sklearn-color-background);\n", "  border-radius: 1em;\n", "  height: 1em;\n", "  width: 1em;\n", "  text-decoration: none !important;\n", "  margin-left: 1ex;\n", "  /* unfitted */\n", "  border: var(--sklearn-color-unfitted-level-1) 1pt solid;\n", "  color: var(--sklearn-color-unfitted-level-1);\n", "}\n", "\n", ".sk-estimator-doc-link.fitted,\n", "a:link.sk-estimator-doc-link.fitted,\n", "a:visited.sk-estimator-doc-link.fitted {\n", "  /* fitted */\n", "  border: var(--sklearn-color-fitted-level-1) 1pt solid;\n", "  color: var(--sklearn-color-fitted-level-1);\n", "}\n", "\n", "/* On hover */\n", "div.sk-estimator:hover .sk-estimator-doc-link:hover,\n", ".sk-estimator-doc-link:hover,\n", "div.sk-label-container:hover .sk-estimator-doc-link:hover,\n", ".sk-estimator-doc-link:hover {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-3);\n", "  color: var(--sklearn-color-background);\n", "  text-decoration: none;\n", "}\n", "\n", "div.sk-estimator.fitted:hover .sk-estimator-doc-link.fitted:hover,\n", ".sk-estimator-doc-link.fitted:hover,\n", "div.sk-label-container:hover .sk-estimator-doc-link.fitted:hover,\n", ".sk-estimator-doc-link.fitted:hover {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-3);\n", "  color: var(--sklearn-color-background);\n", "  text-decoration: none;\n", "}\n", "\n", "/* Span, style for the box shown on hovering the info icon */\n", ".sk-estimator-doc-link span {\n", "  display: none;\n", "  z-index: 9999;\n", "  position: relative;\n", "  font-weight: normal;\n", "  right: .2ex;\n", "  padding: .5ex;\n", "  margin: .5ex;\n", "  width: min-content;\n", "  min-width: 20ex;\n", "  max-width: 50ex;\n", "  color: var(--sklearn-color-text);\n", "  box-shadow: 2pt 2pt 4pt #999;\n", "  /* unfitted */\n", "  background: var(--sklearn-color-unfitted-level-0);\n", "  border: .5pt solid var(--sklearn-color-unfitted-level-3);\n", "}\n", "\n", ".sk-estimator-doc-link.fitted span {\n", "  /* fitted */\n", "  background: var(--sklearn-color-fitted-level-0);\n", "  border: var(--sklearn-color-fitted-level-3);\n", "}\n", "\n", ".sk-estimator-doc-link:hover span {\n", "  display: block;\n", "}\n", "\n", "/* \"?\"-specific style due to the `<a>` HTML tag */\n", "\n", "#sk-container-id-1 a.estimator_doc_link {\n", "  float: right;\n", "  font-size: 1rem;\n", "  line-height: 1em;\n", "  font-family: monospace;\n", "  background-color: var(--sklearn-color-background);\n", "  border-radius: 1rem;\n", "  height: 1rem;\n", "  width: 1rem;\n", "  text-decoration: none;\n", "  /* unfitted */\n", "  color: var(--sklearn-color-unfitted-level-1);\n", "  border: var(--sklearn-color-unfitted-level-1) 1pt solid;\n", "}\n", "\n", "#sk-container-id-1 a.estimator_doc_link.fitted {\n", "  /* fitted */\n", "  border: var(--sklearn-color-fitted-level-1) 1pt solid;\n", "  color: var(--sklearn-color-fitted-level-1);\n", "}\n", "\n", "/* On hover */\n", "#sk-container-id-1 a.estimator_doc_link:hover {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-3);\n", "  color: var(--sklearn-color-background);\n", "  text-decoration: none;\n", "}\n", "\n", "#sk-container-id-1 a.estimator_doc_link.fitted:hover {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-3);\n", "}\n", "</style><div id=\"sk-container-id-1\" class=\"sk-top-container\"><div class=\"sk-text-repr-fallback\"><pre>LogisticRegression()</pre><b>In a Jupyter environment, please rerun this cell to show the HTML representation or trust the notebook. <br />On GitHub, the HTML representation is unable to render, please try loading this page with nbviewer.org.</b></div><div class=\"sk-container\" hidden><div class=\"sk-item\"><div class=\"sk-estimator fitted sk-toggleable\"><input class=\"sk-toggleable__control sk-hidden--visually\" id=\"sk-estimator-id-1\" type=\"checkbox\" checked><label for=\"sk-estimator-id-1\" class=\"sk-toggleable__label fitted sk-toggleable__label-arrow fitted\">&nbsp;&nbsp;LogisticRegression<a class=\"sk-estimator-doc-link fitted\" rel=\"noreferrer\" target=\"_blank\" href=\"https://scikit-learn.org/1.5/modules/generated/sklearn.linear_model.LogisticRegression.html\">?<span>Documentation for LogisticRegression</span></a><span class=\"sk-estimator-doc-link fitted\">i<span>Fitted</span></span></label><div class=\"sk-toggleable__content fitted\"><pre>LogisticRegression()</pre></div> </div></div></div></div>"], "text/plain": ["LogisticRegression()"]}, "execution_count": 28, "metadata": {}, "output_type": "execute_result"}], "source": ["from sklearn import linear_model#调用sklearn的线性回归包\n", "model = linear_model.LogisticRegression(penalty='l2', C=1.0)\n", "model.fit(X2, y2.ravel())"]}, {"cell_type": "code", "execution_count": 29, "metadata": {}, "outputs": [{"data": {"text/plain": ["0.6610169491525424"]}, "execution_count": 29, "metadata": {}, "output_type": "execute_result"}], "source": ["model.score(X2, y2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["这就是练习2的全部！ 敬请期待下一个练习：多类图像分类。"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.9"}}, "nbformat": 4, "nbformat_minor": 1}