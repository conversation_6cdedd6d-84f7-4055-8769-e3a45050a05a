{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 机器学习练习 4 - 神经网络"]}, {"cell_type": "markdown", "metadata": {}, "source": ["本章代码涵盖了基于Python的解决方案，用于Coursera机器学习课程的第四个编程练习。 请参考[练习文本](ex4.pdf)了解详细的说明和公式。\n", "\n", "代码修改并注释：黄海广，<EMAIL>"]}, {"cell_type": "markdown", "metadata": {}, "source": ["对于这个练习，我们将再次处理手写数字数据集，这次使用反向传播的前馈神经网络。 我们将通过反向传播算法实现神经网络成本函数和梯度计算的非正则化和正则化版本。 我们还将实现随机权重初始化和使用网络进行预测的方法。\n", "\n", "由于我们在练习3中使用的数据集是相同的，所以我们将重新使用代码来加载数据。"]}, {"cell_type": "code", "execution_count": 24, "metadata": {"collapsed": true}, "outputs": [], "source": ["import numpy as np\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "from scipy.io import loadmat"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'__header__': b'MATLAB 5.0 MAT-file, Platform: GLNXA64, Created on: Sun Oct 16 13:09:09 2011',\n", " '__version__': '1.0',\n", " '__globals__': [],\n", " 'X': array([[0., 0., 0., ..., 0., 0., 0.],\n", "        [0., 0., 0., ..., 0., 0., 0.],\n", "        [0., 0., 0., ..., 0., 0., 0.],\n", "        ...,\n", "        [0., 0., 0., ..., 0., 0., 0.],\n", "        [0., 0., 0., ..., 0., 0., 0.],\n", "        [0., 0., 0., ..., 0., 0., 0.]]),\n", " 'y': array([[10],\n", "        [10],\n", "        [10],\n", "        ...,\n", "        [ 9],\n", "        [ 9],\n", "        [ 9]], dtype=uint8)}"]}, "execution_count": 25, "metadata": {}, "output_type": "execute_result"}], "source": ["data = loadmat('ex4data1.mat')\n", "data"]}, {"cell_type": "markdown", "metadata": {}, "source": ["由于我们以后需要这些（并将经常使用它们），我们先来创建一些有用的变量。"]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [{"data": {"text/plain": ["((5000, 400), (5000, 1))"]}, "execution_count": 26, "metadata": {}, "output_type": "execute_result"}], "source": ["X = data['X']\n", "y = data['y']\n", "\n", "X.shape, y.shape#看下维度"]}, {"cell_type": "markdown", "metadata": {}, "source": ["我们也需要对我们的y标签进行一次one-hot 编码。 one-hot 编码将类标签n（k类）转换为长度为k的向量，其中索引n为“hot”（1），而其余为0。 Scikitlearn有一个内置的实用程序，我们可以使用这个。"]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [{"data": {"text/plain": ["(5000, 10)"]}, "execution_count": 27, "metadata": {}, "output_type": "execute_result"}], "source": ["from sklearn.preprocessing import OneHotEncoder\n", "encoder = OneHotEncoder(sparse_output=False)\n", "y_onehot = encoder.fit_transform(y)\n", "y_onehot.shape"]}, {"cell_type": "code", "execution_count": 28, "metadata": {}, "outputs": [{"data": {"text/plain": ["(array([10], dtype=uint8), array([0., 0., 0., 0., 0., 0., 0., 0., 0., 1.]))"]}, "execution_count": 28, "metadata": {}, "output_type": "execute_result"}], "source": ["y[0], y_onehot[0,:]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["我们要为此练习构建的神经网络具有与我们的实例数据（400 +偏置单元）大小匹配的输入层，25个单位的隐藏层（带有偏置单元的26个），以及一个输出层， 10个单位对应我们的一个one-hot编码类标签。 有关网络架构的更多详细信息和图像，请参阅“练习”文件夹中的PDF。\n", "\n", "我们需要实现的第一件是评估一组给定的网络参数的损失的代价函数。 源函数在练习文本中（看起来很吓人）。 以下是代价函数的代码。"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# sigmoid 函数\n", "g 代表一个常用的逻辑函数（logistic function）为S形函数（Sigmoid function），公式为：\n", "$$g(z) = \\frac{1}{1+e^{-z}}$$\n", "合起来，我们得到逻辑回归模型的假设函数：\n", "$$h_{\\theta}(x) = \\frac{1}{1+e^{-\\theta^{T}X}}$$"]}, {"cell_type": "code", "execution_count": 29, "metadata": {"collapsed": true}, "outputs": [], "source": ["def sigmoid(z):\n", "    return 1 / (1 + np.exp(-z))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 前向传播函数\n", "> (400 + 1) -> (25 + 1) -> (10)\n", "\n", "<img style=\"float: left;\" src=\"../img/nn_model.png\">"]}, {"cell_type": "code", "execution_count": 30, "metadata": {"collapsed": true}, "outputs": [], "source": ["def forward_propagate(X, theta1, theta2):\n", "    m = X.shape[0]\n", "    \n", "    a1 = np.insert(X, 0, values=np.ones(m), axis=1)\n", "    z2 = a1 * theta1.T\n", "    a2 = np.insert(sigmoid(z2), 0, values=np.ones(m), axis=1)\n", "    z3 = a2 * theta2.T\n", "    h = sigmoid(z3)\n", "    \n", "    return a1, z2, a2, z3, h"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 代价函数\n", "<img style=\"float: left;\" src=\"../img/nn_cost.png\">"]}, {"cell_type": "code", "execution_count": 31, "metadata": {"collapsed": true}, "outputs": [], "source": ["def cost(params, input_size, hidden_size, num_labels, X, y, learning_rate):\n", "    m = X.shape[0]\n", "    X = np.matrix(X)\n", "    y = np.matrix(y)\n", "    \n", "    # reshape the parameter array into parameter matrices for each layer\n", "    theta1 = np.matrix(np.reshape(params[:hidden_size * (input_size + 1)], (hidden_size, (input_size + 1))))\n", "    theta2 = np.matrix(np.reshape(params[hidden_size * (input_size + 1):], (num_labels, (hidden_size + 1))))\n", "    \n", "    # run the feed-forward pass\n", "    a1, z2, a2, z3, h = forward_propagate(X, theta1, theta2)\n", "    \n", "    # compute the cost\n", "    J = 0\n", "    for i in range(m):\n", "        first_term = np.multiply(-y[i,:], np.log(h[i,:]))\n", "        second_term = np.multiply((1 - y[i,:]), np.log(1 - h[i,:]))\n", "        J += np.sum(first_term - second_term)\n", "    \n", "    J = J / m\n", "    \n", "    return J"]}, {"cell_type": "markdown", "metadata": {}, "source": ["这个Sigmoid函数我们以前使用过。 前向传播函数计算给定当前参数的每个训练实例的假设。 它的输出形状应该与y的一个one-hot编码相同。 "]}, {"cell_type": "code", "execution_count": 32, "metadata": {}, "outputs": [{"data": {"text/plain": ["((25, 401), (10, 26))"]}, "execution_count": 32, "metadata": {}, "output_type": "execute_result"}], "source": ["# 初始化设置\n", "input_size = 400\n", "hidden_size = 25\n", "num_labels = 10\n", "learning_rate = 1\n", "\n", "# 随机初始化完整网络参数大小的参数数组\n", "params = (np.random.random(size=hidden_size * (input_size + 1) + num_labels * (hidden_size + 1)) - 0.5) * 0.25\n", "\n", "m = X.shape[0]\n", "X = np.matrix(X)\n", "y = np.matrix(y)\n", "\n", "# 将参数数组解开为每个层的参数矩阵\n", "theta1 = np.matrix(np.reshape(params[:hidden_size * (input_size + 1)], (hidden_size, (input_size + 1))))\n", "theta2 = np.matrix(np.reshape(params[hidden_size * (input_size + 1):], (num_labels, (hidden_size + 1))))\n", "\n", "theta1.shape, theta2.shape"]}, {"cell_type": "code", "execution_count": 33, "metadata": {}, "outputs": [{"data": {"text/plain": ["((5000, 401), (5000, 25), (5000, 26), (5000, 10), (5000, 10))"]}, "execution_count": 33, "metadata": {}, "output_type": "execute_result"}], "source": ["a1, z2, a2, z3, h = forward_propagate(X, theta1, theta2)\n", "a1.shape, z2.shape, a2.shape, z3.shape, h.shape"]}, {"cell_type": "markdown", "metadata": {}, "source": ["代价函数在计算假设矩阵h之后，应用代价函数来计算y和h之间的总误差。"]}, {"cell_type": "code", "execution_count": 34, "metadata": {}, "outputs": [{"data": {"text/plain": ["6.8511220219382505"]}, "execution_count": 34, "metadata": {}, "output_type": "execute_result"}], "source": ["cost(params, input_size, hidden_size, num_labels, X, y_onehot, learning_rate)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 正则化代价函数\n", "我们的下一步是增加代价函数的正则化。  它实际上并不像看起来那么复杂 - 事实上，正则化术语只是我们已经计算出的代价的一个补充。 下面是修改后的代价函数。\n", "<img style=\"float: left;\" src=\"../img/nn_regcost.png\">"]}, {"cell_type": "code", "execution_count": 35, "metadata": {"collapsed": true}, "outputs": [], "source": ["def cost(params, input_size, hidden_size, num_labels, X, y, learning_rate):\n", "    m = X.shape[0]\n", "    X = np.matrix(X)\n", "    y = np.matrix(y)\n", "    \n", "    # reshape the parameter array into parameter matrices for each layer\n", "    theta1 = np.matrix(np.reshape(params[:hidden_size * (input_size + 1)], (hidden_size, (input_size + 1))))\n", "    theta2 = np.matrix(np.reshape(params[hidden_size * (input_size + 1):], (num_labels, (hidden_size + 1))))\n", "    \n", "    # run the feed-forward pass\n", "    a1, z2, a2, z3, h = forward_propagate(X, theta1, theta2)\n", "    \n", "    # compute the cost\n", "    J = 0\n", "    for i in range(m):\n", "        first_term = np.multiply(-y[i,:], np.log(h[i,:]))\n", "        second_term = np.multiply((1 - y[i,:]), np.log(1 - h[i,:]))\n", "        J += np.sum(first_term - second_term)\n", "    \n", "    J = J / m\n", "    \n", "    # add the cost regularization term\n", "    J += (float(learning_rate) / (2 * m)) * (np.sum(np.power(theta1[:,1:], 2)) + np.sum(np.power(theta2[:,1:], 2)))\n", "    \n", "    return J"]}, {"cell_type": "code", "execution_count": 36, "metadata": {}, "outputs": [{"data": {"text/plain": ["6.856464500806818"]}, "execution_count": 36, "metadata": {}, "output_type": "execute_result"}], "source": ["cost(params, input_size, hidden_size, num_labels, X, y_onehot, learning_rate)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["接下来是反向传播算法。 反向传播参数更新计算将减少训练数据上的网络误差。 我们需要的第一件事是计算我们之前创建的Sigmoid函数的梯度的函数。"]}, {"cell_type": "code", "execution_count": 37, "metadata": {"collapsed": true}, "outputs": [], "source": ["def sigmoid_gradient(z):\n", "    return np.multiply(sigmoid(z), (1 - sigmoid(z)))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["现在我们准备好实施反向传播来计算梯度。 由于反向传播所需的计算是代价函数中所需的计算过程，我们实际上将扩展代价函数以执行反向传播并返回代价和梯度。"]}, {"cell_type": "code", "execution_count": 38, "metadata": {"collapsed": true}, "outputs": [], "source": ["def backprop(params, input_size, hidden_size, num_labels, X, y, learning_rate):\n", "    m = X.shape[0]\n", "    X = np.matrix(X)\n", "    y = np.matrix(y)\n", "    \n", "    # reshape the parameter array into parameter matrices for each layer\n", "    theta1 = np.matrix(np.reshape(params[:hidden_size * (input_size + 1)], (hidden_size, (input_size + 1))))\n", "    theta2 = np.matrix(np.reshape(params[hidden_size * (input_size + 1):], (num_labels, (hidden_size + 1))))\n", "    \n", "    # run the feed-forward pass\n", "    a1, z2, a2, z3, h = forward_propagate(X, theta1, theta2)\n", "    \n", "    # initializations\n", "    J = 0\n", "    delta1 = np.zeros(theta1.shape)  # (25, 401)\n", "    delta2 = np.zeros(theta2.shape)  # (10, 26)\n", "    \n", "    # compute the cost\n", "    for i in range(m):\n", "        first_term = np.multiply(-y[i,:], np.log(h[i,:]))\n", "        second_term = np.multiply((1 - y[i,:]), np.log(1 - h[i,:]))\n", "        J += np.sum(first_term - second_term)\n", "    \n", "    J = J / m\n", "    \n", "    # add the cost regularization term\n", "    J += (float(learning_rate) / (2 * m)) * (np.sum(np.power(theta1[:,1:], 2)) + np.sum(np.power(theta2[:,1:], 2)))\n", "    \n", "    # perform backpropagation\n", "    for t in range(m):\n", "        a1t = a1[t,:]  # (1, 401)\n", "        z2t = z2[t,:]  # (1, 25)\n", "        a2t = a2[t,:]  # (1, 26)\n", "        ht = h[t,:]  # (1, 10)\n", "        yt = y[t,:]  # (1, 10)\n", "        \n", "        d3t = ht - yt  # (1, 10)\n", "        \n", "        z2t = np.insert(z2t, 0, values=np.ones(1))  # (1, 26)\n", "        d2t = np.multiply((theta2.T * d3t.T).T, sigmoid_gradient(z2t))  # (1, 26)\n", "        \n", "        delta1 = delta1 + (d2t[:,1:]).T * a1t\n", "        delta2 = delta2 + d3t.T * a2t\n", "        \n", "    delta1 = delta1 / m\n", "    delta2 = delta2 / m\n", "    \n", "    # unravel the gradient matrices into a single array\n", "    grad = np.concatenate((np.ravel(delta1), np.ravel(delta2)))\n", "    \n", "    return J, grad"]}, {"cell_type": "markdown", "metadata": {}, "source": ["反向传播计算的最难的部分（除了理解为什么我们正在做所有这些计算）是获得正确矩阵维度。 顺便说一下，你容易混淆了A * B与np.multiply（A，B）使用。 基本上前者是矩阵乘法，后者是元素乘法（除非A或B是标量值，在这种情况下没关系）。\n", "无论如何，让我们测试一下，以确保函数返回我们期望的。"]}, {"cell_type": "code", "execution_count": 39, "metadata": {}, "outputs": [{"data": {"text/plain": ["(6.856464500806818, (10285,))"]}, "execution_count": 39, "metadata": {}, "output_type": "execute_result"}], "source": ["J, grad = backprop(params, input_size, hidden_size, num_labels, X, y_onehot, learning_rate)\n", "J, grad.shape"]}, {"cell_type": "markdown", "metadata": {}, "source": ["我们还需要对反向传播函数进行一个修改，即将梯度计算加正则化。 最后的正式版本如下。"]}, {"cell_type": "code", "execution_count": 42, "metadata": {"collapsed": true}, "outputs": [], "source": ["def backprop(params, input_size, hidden_size, num_labels, X, y, learning_rate):\n", "    m = X.shape[0]\n", "    X = np.matrix(X)\n", "    y = np.matrix(y)\n", "    \n", "    # reshape the parameter array into parameter matrices for each layer\n", "    theta1 = np.matrix(np.reshape(params[:hidden_size * (input_size + 1)], (hidden_size, (input_size + 1))))\n", "    theta2 = np.matrix(np.reshape(params[hidden_size * (input_size + 1):], (num_labels, (hidden_size + 1))))\n", "    \n", "    # run the feed-forward pass\n", "    a1, z2, a2, z3, h = forward_propagate(X, theta1, theta2)\n", "    \n", "    # initializations\n", "    J = 0\n", "    delta1 = np.zeros(theta1.shape)  # (25, 401)\n", "    delta2 = np.zeros(theta2.shape)  # (10, 26)\n", "    \n", "    # compute the cost\n", "    for i in range(m):\n", "        first_term = np.multiply(-y[i,:], np.log(h[i,:]))\n", "        second_term = np.multiply((1 - y[i,:]), np.log(1 - h[i,:]))\n", "        J += np.sum(first_term - second_term)\n", "    \n", "    J = J / m\n", "    \n", "    # add the cost regularization term\n", "    J += (float(learning_rate) / (2 * m)) * (np.sum(np.power(theta1[:,1:], 2)) + np.sum(np.power(theta2[:,1:], 2)))\n", "    \n", "    # perform backpropagation\n", "    for t in range(m):\n", "        a1t = a1[t,:]  # (1, 401)\n", "        z2t = z2[t,:]  # (1, 25)\n", "        a2t = a2[t,:]  # (1, 26)\n", "        ht = h[t,:]  # (1, 10)\n", "        yt = y[t,:]  # (1, 10)\n", "        \n", "        d3t = ht - yt  # (1, 10)\n", "        \n", "        z2t = np.insert(z2t, 0, values=np.ones(1))  # (1, 26)\n", "        # 修复：正确的反向传播计算\n", "        d2t = np.multiply((theta2.T * d3t.T).T, sigmoid_gradient(z2t))  # (1, 26)\n", "        \n", "        delta1 = delta1 + (d2t[:,1:]).T * a1t\n", "        delta2 = delta2 + d3t.T * a2t\n", "        \n", "    delta1 = delta1 / m\n", "    delta2 = delta2 / m\n", "    \n", "    # add the gradient regularization term\n", "    delta1[:,1:] = delta1[:,1:] + (theta1[:,1:] * learning_rate) / m\n", "    delta2[:,1:] = delta2[:,1:] + (theta2[:,1:] * learning_rate) / m\n", "    \n", "    # unravel the gradient matrices into a single array\n", "    grad = np.concatenate((np.ravel(delta1), np.ravel(delta2)))\n", "    \n", "    return J, grad"]}, {"cell_type": "code", "execution_count": 43, "metadata": {}, "outputs": [{"data": {"text/plain": ["(6.856464500806818, (10285,))"]}, "execution_count": 43, "metadata": {}, "output_type": "execute_result"}], "source": ["J, grad = backprop(params, input_size, hidden_size, num_labels, X, y_onehot, learning_rate)\n", "J, grad.shape"]}, {"cell_type": "markdown", "metadata": {}, "source": ["我们终于准备好训练我们的网络，并用它进行预测。 这与以往的具有多类逻辑回归的练习大致相似。"]}, {"cell_type": "code", "execution_count": 44, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Windows\\Temp\\ipykernel_20732\\769037525.py:4: DeprecationWarning: 'maxiter' has been deprecated in favor of 'maxfun' and will be removed in SciPy 1.11.0.\n", "  fmin = minimize(fun=backprop, x0=params, args=(input_size, hidden_size, num_labels, X, y_onehot, learning_rate),\n"]}, {"data": {"text/plain": [" message: Max. number of function evaluations reached\n", " success: <PERSON><PERSON><PERSON>\n", "  status: 3\n", "     fun: 0.33373786910746006\n", "       x: [-1.815e+00 -4.300e-03 ...  3.069e+00  1.880e+00]\n", "     nit: 19\n", "     jac: [ 1.677e-04 -8.601e-07 ...  1.618e-04  1.898e-04]\n", "    nfev: 251"]}, "execution_count": 44, "metadata": {}, "output_type": "execute_result"}], "source": ["from scipy.optimize import minimize\n", "\n", "# minimize the objective function\n", "fmin = minimize(fun=backprop, x0=params, args=(input_size, hidden_size, num_labels, X, y_onehot, learning_rate), \n", "                method='TNC', jac=True, options={'maxiter': 250})\n", "fmin"]}, {"cell_type": "markdown", "metadata": {}, "source": ["由于目标函数不太可能完全收敛，我们对迭代次数进行了限制。 我们的总代价已经下降到0.5以下，这是算法正常工作的一个很好的指标。 让我们使用它发现的参数，并通过网络转发，以获得一些预测。\n", "\n", "让我们使用它找到的参数，并通过网络前向传播以获得预测。"]}, {"cell_type": "code", "execution_count": 45, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([[10],\n", "       [10],\n", "       [10],\n", "       ...,\n", "       [ 9],\n", "       [ 9],\n", "       [ 9]], dtype=int64)"]}, "execution_count": 45, "metadata": {}, "output_type": "execute_result"}], "source": ["X = np.matrix(X)\n", "theta1 = np.matrix(np.reshape(fmin.x[:hidden_size * (input_size + 1)], (hidden_size, (input_size + 1))))\n", "theta2 = np.matrix(np.reshape(fmin.x[hidden_size * (input_size + 1):], (num_labels, (hidden_size + 1))))\n", "\n", "a1, z2, a2, z3, h = forward_propagate(X, theta1, theta2)\n", "y_pred = np.array(np.argmax(h, axis=1) + 1)\n", "y_pred"]}, {"cell_type": "markdown", "metadata": {}, "source": ["最后，我们可以计算准确度，看看我们训练完毕的神经网络效果怎么样。"]}, {"cell_type": "code", "execution_count": 46, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["accuracy = 99.28%\n"]}], "source": ["correct = [1 if a == b else 0 for (a, b) in zip(y_pred, y)]\n", "accuracy = (sum(map(int, correct)) / float(len(correct)))\n", "print ('accuracy = {0}%'.format(accuracy * 100))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["我们已经成功地实施了一个基本的反向传播神经网络，并用它来分类手写数字图像。 在下一个练习中，我们将介绍另一个强大的监督学习算法，支持向量机。"]}, {"cell_type": "code", "execution_count": 51, "metadata": {"collapsed": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["成功设置字体: SimHei\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1200x1000 with 4 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import matplotlib.pyplot as plt\n", "import numpy as np\n", "import warnings\n", "\n", "def setup_matplotlib_chinese():\n", "    \"\"\"设置matplotlib支持中文\"\"\"\n", "    try:\n", "        # 尝试不同的中文字体\n", "        font_options = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans', 'Arial Unicode MS']\n", "        \n", "        for font in font_options:\n", "            try:\n", "                plt.rcParams['font.sans-serif'] = [font]\n", "                plt.rcParams['axes.unicode_minus'] = False\n", "                \n", "                # 测试是否能正常显示\n", "                fig, ax = plt.subplots(figsize=(1, 1))\n", "                ax.text(0.5, 0.5, '测试中文', ha='center', va='center')\n", "                plt.close(fig)\n", "                \n", "                print(f\"成功设置字体: {font}\")\n", "                return True\n", "            except:\n", "                continue\n", "                \n", "        print(\"警告：无法找到合适的中文字体，将使用英文\")\n", "        return False\n", "        \n", "    except Exception as e:\n", "        print(f\"设置字体时出错: {e}\")\n", "        return False\n", "\n", "# 设置中文支持\n", "use_chinese = setup_matplotlib_chinese()\n", "\n", "def visualize_training_process_adaptive():\n", "    \"\"\"自适应的可视化函数\"\"\"\n", "    fig, axes = plt.subplots(2, 2, figsize=(12, 10))\n", "    \n", "    # 根据是否支持中文选择文本\n", "    if use_chinese:\n", "        texts = {\n", "            'model_def': '1. 模型定义\\n\\nmodel = Sequential([\\n    <PERSON><PERSON>(128),\\n    <PERSON>se(10)\\n])\\n\\n参数：随机初始化',\n", "            'model_def_title': '模型定义',\n", "            'compile': '2. 模型编译\\n\\nmodel.compile(\\n    optimizer=\"adam\",\\n    loss=\"mse\"\\n)\\n\\n只是配置，不训练！',\n", "            'compile_title': '模型编译',\n", "            'training_loop': '3. 训练循环 (model.fit)',\n", "            'steps': ['前向传播\\n(计算预测)', '计算损失', '反向传播\\n(计算梯度)', 'Adam更新\\n(使用梯度)'],\n", "            'loss_title': '训练过程中的损失变化'\n", "        }\n", "    else:\n", "        texts = {\n", "            'model_def': '1. Model Definition\\n\\nmodel = Sequential([\\n    <PERSON><PERSON>(128),\\n    <PERSON>se(10)\\n])\\n\\nParameters: Random Init',\n", "            'model_def_title': 'Model Definition',\n", "            'compile': '2. Model Compile\\n\\nmodel.compile(\\n    optimizer=\"adam\",\\n    loss=\"mse\"\\n)\\n\\nJust Configuration!',\n", "            'compile_title': 'Model Compile',\n", "            'training_loop': '3. Training Loop (model.fit)',\n", "            'steps': ['Forward Pass\\n(Predictions)', 'Compute Loss', 'Backward Pass\\n(Gradients)', 'Adam Update\\n(Use Gradients)'],\n", "            'loss_title': 'Loss During Training'\n", "        }\n", "    \n", "    # 1. Model Definition\n", "    ax1 = axes[0, 0]\n", "    ax1.text(0.5, 0.5, texts['model_def'], \n", "             ha='center', va='center', fontsize=12, \n", "             bbox=dict(boxstyle=\"round,pad=0.3\", facecolor=\"lightblue\"))\n", "    ax1.set_title(texts['model_def_title'])\n", "    ax1.axis('off')\n", "    \n", "    # 2. <PERSON><PERSON><PERSON>\n", "    ax2 = axes[0, 1]\n", "    ax2.text(0.5, 0.5, texts['compile'], \n", "             ha='center', va='center', fontsize=12, \n", "             bbox=dict(boxstyle=\"round,pad=0.3\", facecolor=\"lightgreen\"))\n", "    ax2.set_title(texts['compile_title'])\n", "    ax2.axis('off')\n", "    \n", "    # 3. Training Loop\n", "    ax3 = axes[1, 0]\n", "    ax3.text(0.5, 0.8, texts['training_loop'], fontsize=14, ha='center', weight='bold')\n", "    \n", "    # Draw training flow\n", "    steps = texts['steps']\n", "    y_positions = [0.6, 0.4, 0.2, 0.0]\n", "    \n", "    for i, (step, y) in enumerate(zip(steps, y_positions)):\n", "        ax3.text(0.5, y, f'{i+1}. {step}', ha='center', va='center', \n", "                fontsize=11, bbox=dict(boxstyle=\"round,pad=0.3\", facecolor=\"lightyellow\"))\n", "        if i < len(steps) - 1:\n", "            ax3.arrow(0.5, y-0.05, 0, -0.1, head_width=0.03, head_length=0.02, fc='black', ec='black')\n", "    \n", "    ax3.set_xlim(0, 1)\n", "    ax3.set_ylim(-0.1, 0.9)\n", "    ax3.axis('off')\n", "    \n", "    # 4. Loss Curve\n", "    ax4 = axes[1, 1]\n", "    epochs = np.arange(0, 100)\n", "    loss = 10 * np.exp(-0.05 * epochs) + 0.1 + 0.5 * np.random.randn(100) * np.exp(-0.03 * epochs)\n", "    ax4.plot(epochs, loss, 'b-', linewidth=2)\n", "    ax4.set_xlabel('Epochs')\n", "    ax4.set_ylabel('Loss')\n", "    ax4.set_title(texts['loss_title'])\n", "    ax4.grid(True, alpha=0.3)\n", "    \n", "    plt.tight_layout()\n", "    plt.show()\n", "\n", "# 运行自适应版本\n", "visualize_training_process_adaptive()\n"]}, {"cell_type": "code", "execution_count": 49, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["模型已编译，使用 adam 优化器和 categorical_crossentropy 损失函数\n", "注意：此时参数仍是随机的，没有进行任何训练！\n", "\n", "开始训练...\n", "Epoch 0, Loss: 2.3042\n", "Epoch 2, Loss: 2.3040\n", "Epoch 4, Loss: 2.3038\n"]}], "source": ["# 展示 Keras 背后实际发生的事情\n", "class SimpleModel:\n", "    def __init__(self):\n", "        # 初始化参数（compile时不会改变这些）\n", "        self.W1 = np.random.randn(784, 128) * 0.01\n", "        self.b1 = np.zeros((1, 128))\n", "        self.W2 = np.random.randn(128, 10) * 0.01\n", "        self.b2 = np.zeros((1, 10))\n", "        \n", "    def compile(self, optimizer='adam', loss='mse'):\n", "        \"\"\"编译只是保存配置\"\"\"\n", "        self.optimizer_name = optimizer\n", "        self.loss_name = loss\n", "        print(f\"模型已编译，使用 {optimizer} 优化器和 {loss} 损失函数\")\n", "        print(\"注意：此时参数仍是随机的，没有进行任何训练！\")\n", "        \n", "    def fit(self, X, y, epochs=10):\n", "        \"\"\"fit才是真正的训练\"\"\"\n", "        print(\"\\n开始训练...\")\n", "        \n", "        for epoch in range(epochs):\n", "            # 1. 前向传播\n", "            z1 = X @ self.W1 + self.b1\n", "            a1 = np.maximum(0, z1)  # ReLU\n", "            z2 = a1 @ self.W2 + self.b2\n", "            y_pred = self.softmax(z2)\n", "            \n", "            # 2. 计算损失\n", "            loss = self.cross_entropy(y, y_pred)\n", "            \n", "            # 3. 反向传播（这是关键！）\n", "            # 计算梯度\n", "            dz2 = y_pred - y\n", "            dW2 = a1.T @ dz2 / len(X)\n", "            db2 = np.mean(dz2, axis=0, keepdims=True)\n", "            \n", "            da1 = dz2 @ self.W2.T\n", "            dz1 = da1 * (z1 > 0)  # ReLU导数\n", "            dW1 = X.T @ dz1 / len(X)\n", "            db1 = np.mean(dz1, axis=0, keepdims=True)\n", "            \n", "            # 4. 使用Adam更新参数（需要梯度！）\n", "            if self.optimizer_name == 'adam':\n", "                # Adam需要梯度来计算更新\n", "                self.W1 -= 0.001 * dW1  # 简化的更新\n", "                self.b1 -= 0.001 * db1\n", "                self.W2 -= 0.001 * dW2\n", "                self.b2 -= 0.001 * db2\n", "            \n", "            if epoch % 2 == 0:\n", "                print(f\"Epoch {epoch}, Loss: {loss:.4f}\")\n", "    \n", "    def softmax(self, x):\n", "        exp_x = np.exp(x - np.max(x, axis=1, keepdims=True))\n", "        return exp_x / np.sum(exp_x, axis=1, keepdims=True)\n", "    \n", "    def cross_entropy(self, y_true, y_pred):\n", "        return -np.mean(np.sum(y_true * np.log(y_pred + 1e-8), axis=1))\n", "\n", "# 使用示例\n", "model = SimpleModel()\n", "model.compile(optimizer='adam', loss='categorical_crossentropy')\n", "\n", "# 创建假数据\n", "X = np.random.randn(100, 784)\n", "y = np.eye(10)[np.random.randint(0, 10, 100)]  # one-hot编码\n", "\n", "# 训练（这里才真正更新参数）\n", "model.fit(X, y, epochs=6)\n"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.9"}}, "nbformat": 4, "nbformat_minor": 1}