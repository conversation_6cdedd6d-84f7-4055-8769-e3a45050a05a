{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 机器学习练习 1 - 线性回归"]}, {"cell_type": "markdown", "metadata": {}, "source": ["这个是另一位大牛写的，作业内容在根目录： [作业文件](ex1.pdf)\n", "\n", "代码修改并注释：黄海广，<EMAIL>"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 单变量线性回归"]}, {"cell_type": "code", "execution_count": 26, "metadata": {"collapsed": true}, "outputs": [], "source": ["import numpy as np\n", "import pandas as pd\n", "import matplotlib.pyplot as plt"]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Population</th>\n", "      <th>Profit</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>6.1101</td>\n", "      <td>17.5920</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>5.5277</td>\n", "      <td>9.1302</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>8.5186</td>\n", "      <td>13.6620</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>7.0032</td>\n", "      <td>11.8540</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>5.8598</td>\n", "      <td>6.8233</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   Population   Profit\n", "0      6.1101  17.5920\n", "1      5.5277   9.1302\n", "2      8.5186  13.6620\n", "3      7.0032  11.8540\n", "4      5.8598   6.8233"]}, "execution_count": 27, "metadata": {}, "output_type": "execute_result"}], "source": ["path =  'ex1data1.txt'\n", "data = pd.read_csv(path, header=None, names=['Population', 'Profit'])\n", "data.head()"]}, {"cell_type": "code", "execution_count": 28, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Population</th>\n", "      <th>Profit</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>count</th>\n", "      <td>97.000000</td>\n", "      <td>97.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>mean</th>\n", "      <td>8.159800</td>\n", "      <td>5.839135</td>\n", "    </tr>\n", "    <tr>\n", "      <th>std</th>\n", "      <td>3.869884</td>\n", "      <td>5.510262</td>\n", "    </tr>\n", "    <tr>\n", "      <th>min</th>\n", "      <td>5.026900</td>\n", "      <td>-2.680700</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25%</th>\n", "      <td>5.707700</td>\n", "      <td>1.986900</td>\n", "    </tr>\n", "    <tr>\n", "      <th>50%</th>\n", "      <td>6.589400</td>\n", "      <td>4.562300</td>\n", "    </tr>\n", "    <tr>\n", "      <th>75%</th>\n", "      <td>8.578100</td>\n", "      <td>7.046700</td>\n", "    </tr>\n", "    <tr>\n", "      <th>max</th>\n", "      <td>22.203000</td>\n", "      <td>24.147000</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["       Population     Profit\n", "count   97.000000  97.000000\n", "mean     8.159800   5.839135\n", "std      3.869884   5.510262\n", "min      5.026900  -2.680700\n", "25%      5.707700   1.986900\n", "50%      6.589400   4.562300\n", "75%      8.578100   7.046700\n", "max     22.203000  24.147000"]}, "execution_count": 28, "metadata": {}, "output_type": "execute_result"}], "source": ["data.describe()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["看下数据长什么样子"]}, {"cell_type": "code", "execution_count": 29, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1200x800 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["data.plot(kind='scatter', x='Population', y='Profit', figsize=(12,8))\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["现在让我们使用梯度下降来实现线性回归，以最小化成本函数。 以下代码示例中实现的方程在“练习”文件夹中的“ex1.pdf”中有详细说明。"]}, {"cell_type": "markdown", "metadata": {}, "source": ["首先，我们将创建一个以参数 $\\theta$ 为特征函数的代价函数：\n", "$$\n", "J\\left( \\theta  \\right)=\\frac{1}{2m}\\sum\\limits_{i=1}^{m}{{{\\left( {{h}_{\\theta }}\\left( {{x}^{(i)}} \\right)-{{y}^{(i)}} \\right)}^{2}}}\n", "$$\n", "其中：\n", "$$\n", "{{h}_{\\theta }}\\left( x \\right)={{\\theta }^{T}}X={{\\theta }_{0}}{{x}_{0}}+{{\\theta }_{1}}{{x}_{1}}+{{\\theta }_{2}}{{x}_{2}}+...+{{\\theta }_{n}}{{x}_{n}}\n", "$$\n"]}, {"cell_type": "code", "execution_count": 30, "metadata": {"collapsed": true}, "outputs": [], "source": ["def computeCost(X, y, theta):\n", "    inner = np.power(((X * theta.T) - y), 2)\n", "    return np.sum(inner) / (2 * len(X))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["让我们在训练集中添加一列，以便我们可以使用向量化的解决方案来计算代价和梯度。"]}, {"cell_type": "code", "execution_count": 31, "metadata": {"collapsed": true}, "outputs": [], "source": ["data.insert(0, 'Ones', 1)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["现在我们来做一些变量初始化。"]}, {"cell_type": "code", "execution_count": 32, "metadata": {"collapsed": true}, "outputs": [], "source": ["# set X (training data) and y (target variable)\n", "cols = data.shape[1]\n", "X = data.iloc[:,0:cols-1]#X是所有行，去掉最后一列\n", "y = data.iloc[:,cols-1:cols]#X是所有行，最后一列"]}, {"cell_type": "markdown", "metadata": {}, "source": ["观察下 X (训练集) and y (目标变量)是否正确."]}, {"cell_type": "code", "execution_count": 33, "metadata": {"scrolled": true}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Ones</th>\n", "      <th>Population</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>6.1101</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>1</td>\n", "      <td>5.5277</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>1</td>\n", "      <td>8.5186</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>1</td>\n", "      <td>7.0032</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>1</td>\n", "      <td>5.8598</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   Ones  Population\n", "0     1      6.1101\n", "1     1      5.5277\n", "2     1      8.5186\n", "3     1      7.0032\n", "4     1      5.8598"]}, "execution_count": 33, "metadata": {}, "output_type": "execute_result"}], "source": ["X.head()#head()是观察前5行"]}, {"cell_type": "code", "execution_count": 34, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Profit</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>17.5920</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>9.1302</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>13.6620</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>11.8540</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>6.8233</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    Profit\n", "0  17.5920\n", "1   9.1302\n", "2  13.6620\n", "3  11.8540\n", "4   6.8233"]}, "execution_count": 34, "metadata": {}, "output_type": "execute_result"}], "source": ["y.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["代价函数是应该是numpy矩阵，所以我们需要转换X和Y，然后才能使用它们。 我们还需要初始化theta。"]}, {"cell_type": "code", "execution_count": 35, "metadata": {"collapsed": true}, "outputs": [], "source": ["X = np.matrix(X.values)\n", "y = np.matrix(y.values)\n", "theta = np.matrix(np.array([0,0]))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["theta 是一个(1,2)矩阵"]}, {"cell_type": "code", "execution_count": 36, "metadata": {}, "outputs": [{"data": {"text/plain": ["matrix([[0, 0]])"]}, "execution_count": 36, "metadata": {}, "output_type": "execute_result"}], "source": ["theta"]}, {"cell_type": "markdown", "metadata": {}, "source": ["看下维度"]}, {"cell_type": "code", "execution_count": 37, "metadata": {}, "outputs": [{"data": {"text/plain": ["((97, 2), (1, 2), (97, 1))"]}, "execution_count": 37, "metadata": {}, "output_type": "execute_result"}], "source": ["X.shape, theta.shape, y.shape"]}, {"cell_type": "markdown", "metadata": {}, "source": ["计算代价函数 (theta初始值为0)."]}, {"cell_type": "code", "execution_count": 38, "metadata": {}, "outputs": [{"data": {"text/plain": ["32.072733877455676"]}, "execution_count": 38, "metadata": {}, "output_type": "execute_result"}], "source": ["computeCost(X, y, theta)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# batch gradient decent（批量梯度下降）\n", "$${{\\theta }_{j}}:={{\\theta }_{j}}-\\alpha \\frac{\\partial }{\\partial {{\\theta }_{j}}}J\\left( \\theta  \\right)$$"]}, {"cell_type": "code", "execution_count": 39, "metadata": {"collapsed": true}, "outputs": [], "source": ["def gradientDescent(X, y, theta, alpha, iters):\n", "    temp = np.matrix(np.zeros(theta.shape))\n", "    parameters = int(theta.ravel().shape[1])\n", "    cost = np.zeros(iters)\n", "    \n", "    for i in range(iters):\n", "        error = (X * theta.T) - y\n", "        \n", "        for j in range(parameters):\n", "            term = np.multiply(error, X[:,j])\n", "            temp[0,j] = theta[0,j] - ((alpha / len(X)) * np.sum(term))\n", "            \n", "        theta = temp\n", "        cost[i] = computeCost(X, y, theta)\n", "        \n", "    return theta, cost"]}, {"cell_type": "markdown", "metadata": {}, "source": ["初始化一些附加变量 - 学习速率α和要执行的迭代次数。"]}, {"cell_type": "code", "execution_count": 40, "metadata": {"collapsed": true}, "outputs": [], "source": ["alpha = 0.01\n", "iters = 1000"]}, {"cell_type": "markdown", "metadata": {}, "source": ["现在让我们运行梯度下降算法来将我们的参数θ适合于训练集。"]}, {"cell_type": "code", "execution_count": 41, "metadata": {}, "outputs": [{"data": {"text/plain": ["matrix([[-3.24140214,  1.1272942 ]])"]}, "execution_count": 41, "metadata": {}, "output_type": "execute_result"}], "source": ["g, cost = gradientDescent(X, y, theta, alpha, iters)\n", "g"]}, {"cell_type": "markdown", "metadata": {}, "source": ["最后，我们可以使用我们拟合的参数计算训练模型的代价函数（误差）。"]}, {"cell_type": "code", "execution_count": 42, "metadata": {}, "outputs": [{"data": {"text/plain": ["4.515955503078914"]}, "execution_count": 42, "metadata": {}, "output_type": "execute_result"}], "source": ["computeCost(X, y, g)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["现在我们来绘制线性模型以及数据，直观地看出它的拟合。"]}, {"cell_type": "code", "execution_count": 43, "metadata": {}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAA+UAAAK7CAYAAACUMC65AAAAOXRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjcuMCwgaHR0cHM6Ly9tYXRwbG90bGliLm9yZy88F64QAAAACXBIWXMAAA9hAAAPYQGoP6dpAACiJklEQVR4nOzdeVyVZd7H8c8BEdQQ00Qw16xMs83Ksk2tLK3MsqbV0vZsX8xSnxlzJteyskXbrcb2Mmdsytax1bJymsmspkxtwyxNNBNUuJ8/7gFFQQGB+wCf9+vF64mL+5zz45wzPnzPdV2/KxYEQYAkSZIkSapyCVEXIEmSJElSbWUolyRJkiQpIoZySZIkSZIiYiiXJEmSJCkihnJJkiRJkiJiKJckSZIkKSKGckmSJEmSImIolyRJkiQpIoZySZIkSZIiYiiXpBru4YcfJhaLFX7VqVOHFi1acM455/DDDz9USQ1t2rRh4MCBhd/PmjWLWCzGrFmzynQ/7733HjfeeCMrVqyo0PoABg4cSJs2bbZ6Xffu3Ys8n/Xq1WOvvfbi9ttvJz8/v0Jrev3119lvv/1o0KABsViM6dOnF76eixYtKrzu8ccf5/bbb6/Qx65oAwcOLPK8JScn0759e0aMGEFOTk7U5QGbv0/LYvTo0UyfPn2z8fK+1ytCEAQ8+eSTHHrooaSnp5OSkkKLFi04+uijeeCBB4pcG4vFuPHGG6u8RkmSoVySao0pU6Ywe/ZsXn31VS644AKeeOIJDj30UFavXl3ltXTu3JnZs2fTuXPnMt3uvffeY+TIkZUSystip512Yvbs2cyePZunnnqKHXfckauvvpqhQ4dW2GMEQcApp5xCUlISf//735k9ezbdunXj2GOPZfbs2WRmZhZeWx1COUC9evUKn7fp06dzwAEH8Oc//5kBAwZEXdo2KymUl/e9XhGGDh3K6aefTocOHXjggQd46aWXuOmmm2jWrBl/+9vfilw7e/Zszj///CqvUZIEdaIuQJJUNTp16sR+++0HQI8ePcjLy+Mvf/kL06dP58wzzyz2Nr///jv169ev8FoaNmzIgQceWOH3W1Xq1atXpP7evXuz2267cdddd3HTTTeRlJS02W2CICAnJ4d69eqV6jF+/PFHli9fzoknnsgRRxxR5GdNmzbdtl8gIgkJCZs9b4sWLeLpp5/m1ltvZccdd4ywusoR1Xt9zZo13H777Zx99tncd999RX42cODAzVZ1VOf/PUpSdedMuSTVUgV/hC9evBgI/1Dfbrvt+PTTTznqqKNITU0tDINr167lpptuYrfddiM5OZmmTZtyzjnn8PPPPxe5z3Xr1jFkyBAyMjKoX78+hxxyCHPmzNnssUta0vvBBx/Qp08fmjRpQkpKCu3ateOqq64C4MYbb+S6664DoG3btoXLoDe+j6eeeoquXbvSoEEDtttuO44++mj+9a9/bfb4Dz/8MO3btyc5OZkOHTrw6KOPlus5LJCUlMS+++7L77//XvicxGIxLrvsMu655x46dOhAcnIyjzzyCADvvPMORxxxBKmpqdSvX5+DDjqIf/zjH4X3d+ONN9KiRQsArr/+emKxWOHS+k2Xr3fv3p1//OMfLF68uMjy8JKccMIJtG7dutil9gcccECRGd1nnnmGAw44gLS0NOrXr89OO+3Eueeeu03P1aY2fR9+++239O/fn/T09MLXZ8KECUXqXbRoEbFYjPHjxzNq1ChatWpFSkoK++23H6+//nqR+y9pW8KNN964xecJICcnh2uvvZa9996btLQ0GjduTNeuXTebZY7FYqxevZpHHnmk8Pnv3r07UPJ7/e9//ztdu3alfv36pKam0rNnT2bPnl1sjZ999hmnn346aWlpNGvWjHPPPZfs7Owt1r569Wpyc3OLrKjYWEJC0T8BN12+3qZNmyLvp42/Nv5dvvrqK84444wir9fdd9+9xdokSUU5Uy5JtdTXX38NFJ11Xbt2LccffzwXXXQRN9xwA+vXryc/P5++ffvy9ttvM2TIEA466CAWL17MiBEj6N69Ox999FHh7O8FF1zAo48+yuDBg+nZsyfz5s2jX79+rFq1aqv1vPzyy/Tp04cOHTpw66230qpVKxYtWsQrr7wCwPnnn8/y5cu58847mTZtWmHY6NixIxAuH/6///s/zjnnHP7v//6PtWvXcvPNN3PooYcyZ86cwusefvhhzjnnHPr27cuECRPIzs7mxhtvJDc3d7OgUhYLFiygTp06bL/99oVj06dP5+233+ZPf/oTGRkZpKen8+abb9KzZ0/23HNPHnzwQZKTk5k0aRJ9+vThiSee4NRTT+X8889nr732ol+/flx++eWcccYZJCcnF/u4kyZN4sILL2TBggU8//zzW63z3HPPpW/fvrzxxhsceeSRheNffPEFc+bM4Y477gDC5cynnnoqp556KjfeeCMpKSksXryYN954o9zPUXE2fh/+/PPPHHTQQaxdu5a//OUvtGnThhdeeIHBgwezYMECJk2aVOS2d911F61bty7czz9+/Hh69+7Nm2++SdeuXbe5ttzcXJYvX87gwYPZcccdWbt2La+99hr9+vVjypQpnH322UD4XB1++OH06NGDP/7xj0A4Q16Sxx9/nDPPPJOjjjqKJ554gtzcXMaPH0/37t15/fXXOeSQQ4pcf9JJJ3Hqqady3nnn8emnnxZuk3jooYdKfIwddtiBnXfemUmTJpGens4xxxxD+/btt/pBRIHnn3+e3Nzcwu/z8/O5+OKL+eabb2jVqhUA8+fP56CDDqJVq1ZMmDCBjIwMXn75Za644gp++eUXRowYUarHkqRaL5Ak1WhTpkwJgOD9998P1q1bF6xatSp44YUXgqZNmwapqanBkiVLgiAIggEDBgRA8NBDDxW5/RNPPBEAwXPPPVdk/MMPPwyAYNKkSUEQBMHnn38eAMHVV19d5LrHHnssAIIBAwYUjv3zn/8MgOCf//xn4Vi7du2Cdu3aBWvWrCnxd7n55psDIFi4cGGR8W+//TaoU6dOcPnllxcZX7VqVZCRkRGccsopQRAEQV5eXtC8efOgc+fOQX5+fuF1ixYtCpKSkoLWrVuX+NgFunXrFuy+++7BunXrgnXr1gU//vhjcMMNNwRA8Ic//KHwOiBIS0sLli9fXuT2Bx54YJCenh6sWrWqcGz9+vVBp06dghYtWhTWtXDhwgAIbr755iK3L3g9N34Ojj322FLVHgRBsG7duqBZs2bBGWecUWR8yJAhQd26dYNffvklCIIguOWWWwIgWLFiRanud2sGDBgQNGjQoPB5+/nnn4OJEycGsVgs2H///YMgCAqfxw8++KDIbQcNGhTEYrHgyy+/DIJgw3PTvHnzIu+XlStXBo0bNw6OPPLIIo9b3HMzYsSIYNM/g1q3bl3kfbqp9evXB+vWrQvOO++8YJ999inyswYNGhR7203f6wXvwT322CPIy8srvG7VqlVBenp6cNBBB21W4/jx44vc5yWXXBKkpKQUeQ8XZ86cOUGrVq0CIACC1NTU4LjjjgseffTRzW4LBCNGjCjxvi677LKgTp06wYsvvlg4dvTRRwctWrQIsrOzN7s2JSVls/e+JKl4Ll+XpFriwAMPJCkpidTUVI477jgyMjJ46aWXaNasWZHrTjrppCLfv/DCCzRq1Ig+ffqwfv36wq+9996bjIyMwqWs//znPwE2259+yimnUKfOlhdm/fe//2XBggWcd955pKSklPl3e/nll1m/fj1nn312kRpTUlLo1q1bYY1ffvklP/74I2eccUaRGcPWrVtz0EEHlfrxPvvsM5KSkkhKSqJ58+ZMmDCBM888k/vvv7/IdYcffniRmfPVq1fzwQcfcPLJJ7PddtsVjicmJnLWWWfx/fff8+WXX5b59y+LOnXq0L9/f6ZNm1a4BDovL4+//vWv9O3blyZNmgCw//77A+Hr9/TTT1dIp/7Vq1cXPm9Nmzblqquuonfv3oUz/G+88QYdO3akS5cuRW43cOBAgiDYbJa+X79+Rd4vqamp9OnTh7feeou8vLxtrhfCJfwHH3ww2223HXXq1CEpKYkHH3yQzz//vFz3V/AePOuss4qszNhuu+046aSTeP/99/n999+L3Ob4448v8v2ee+5JTk4OS5cu3eJj7b///nz99dfMnDmTYcOG0bVrV15//XXOPvtsjj/+eIIgKFXNY8eO5a677uKee+6hd+/eQLi0//XXX+fEE0+kfv36Rf53d8wxx5CTk8P7779fqvuXpNrO5euSVEs8+uijdOjQgTp16tCsWbNi95rWr19/s2W3P/30EytWrKBu3brF3u8vv/wCwLJlywDIyMgo8vM6deoUBr2SFOzDLthHXVY//fQTsCFIbqog/JRUY8HYxseMbUm7du148sknicVipKSk0LZt22Ib4m36HP/6668EQVDsc9+8efMiNVamc889lwkTJvDkk09y0UUX8fLLL5OVlcU555xTeM1hhx3G9OnTueOOOzj77LPJzc1l9913Z/jw4Zx++unletx69erx1ltvAZCcnEzr1q2LvN+WLVtW7P7vkp6bkl7HtWvX8ttvv5GWllauOgtMmzaNU045hT/84Q9cd911ZGRkUKdOHSZPnrzFpeNbUvA7lPQeyM/P59dffy3yftr0fz8FWxnWrFmz1cdLSkri6KOP5uijjy58/JNPPpkXXniBl156iWOOOWaLt586dSrDhg3jT3/6E+edd16R32P9+vXceeed3HnnncXetuDfBknSlhnKJamW6NChQ2H39ZIUt990hx12oEmTJsycObPY26SmpgIbgsOSJUuKdNFev379VoNmwb7277//fovXlWSHHXYA4Nlnn6V169YlXrdxjZsqbqwkBU3FtmbT53P77bcnISGBrKysza798ccfgQ2/S2UqmI2eMmUKF110EVOmTKF58+YcddRRRa7r27cvffv2JTc3l/fff58xY8Zwxhln0KZNm3Lt2U5ISNji89akSZMyPTclvY5169YtXImQkpJSZG90gdIExqlTp9K2bVueeuqpIq9lcfdXWgXvwZJ+z4SEhCKrKypakyZNuOqqq5g1axbz5s3bYih/9dVXOffccxk4cCAjR44s8rPtt9++cIXHpZdeWuzt27ZtW6G1S1JN5fJ1SdIWHXfccSxbtoy8vDz222+/zb7at28PUNht+rHHHity+6effpr169dv8TF23XVX2rVrx0MPPbTFwFPSDOHRRx9NnTp1WLBgQbE1FgTB9u3bk5mZyRNPPFFk6e7ixYt57733SveEbIMGDRpwwAEHMG3atCK/Q35+PlOnTqVFixbsuuuuZb7f5OTkUs2abuycc87hgw8+4J133mHGjBkMGDCAxMTEEu+/W7dujBs3DqDYjvYV4YgjjmD+/PnMnTu3yPijjz5KLBajR48eRcanTZtGTk5O4ferVq1ixowZHHrooYW/S5s2bVi6dGnhagoIGxq+/PLLW60nFotRt27dIoF8yZIlm3Vfh9K/Bu3bt2fHHXfk8ccfL/IeXL16Nc8991xhR/ZttW7duhI/DCtYel+wAqE4n3zyCSeddBKHH374ZkeqQbiqpkePHvzrX/9izz33LPZ/c1tbISNJCjlTLknaotNOO43HHnuMY445hiuvvJIuXbqQlJTE999/zz//+U/69u3LiSeeSIcOHejfvz+33347SUlJHHnkkcybN49bbrlli52oC9x999306dOHAw88kKuvvppWrVrx7bff8vLLLxcG/T322AOAiRMnMmDAAJKSkmjfvj1t2rThz3/+M8OHD+ebb76hV69ebL/99vz000/MmTOHBg0aMHLkSBISEvjLX/7C+eefz4knnsgFF1zAihUruPHGG4tdCl0ZxowZQ8+ePenRoweDBw+mbt26TJo0iXnz5vHEE0+Uujv2xvbYYw+mTZvG5MmT2Xfffbc6Iw1w+umnc80113D66aeTm5vLwIEDi/z8T3/6E99//z1HHHEELVq0YMWKFUycOJGkpCS6detWeF2dOnXo1q3bZkeRlcfVV1/No48+yrHHHsuf//xnWrduzT/+8Q8mTZrEoEGDNvvAIjExkZ49e3LNNdeQn5/PuHHjWLlyZZFZ3VNPPZU//elPnHbaaVx33XXk5ORwxx13lGrP+XHHHce0adO45JJLOPnkk/nuu+/4y1/+QmZmJl999VWRa/fYYw9mzZrFjBkzyMzMJDU1tfADq40lJCQwfvx4zjzzTI477jguuugicnNzufnmm1mxYgVjx44t57NXVHZ2Nm3atOEPf/gDRx55JC1btuS3335j1qxZTJw4kQ4dOtCvX79ib7ty5UqOOeYY6tWrx+DBg/noo4+K/Lxjx440bNiQiRMncsghh3DooYcyaNAg2rRpw6pVq/j666+ZMWNGhXfql6QaK9I2c5KkSlfQrfvDDz/c4nUF3bGLs27duuCWW24J9tprryAlJSXYbrvtgt122y246KKLgq+++qrwutzc3ODaa68N0tPTg5SUlODAAw8MZs+evVlX6+K6rwdBEMyePTvo3bt3kJaWFiQnJwft2rXbrJv70KFDg+bNmwcJCQmb3cf06dODHj16BA0bNgySk5OD1q1bByeffHLw2muvFbmPBx54INhll12CunXrBrvuumvw0EMPldile1MF3de3BgguvfTSYn/29ttvB4cffnjQoEGDoF69esGBBx4YzJgxo8g1Zem+vnz58uDkk08OGjVqFMRisc26ipfkjDPOCIDg4IMP3uxnL7zwQtC7d+9gxx13DOrWrRukp6cHxxxzTPD2229v9nt269Ztq4+1pffXxhYvXhycccYZQZMmTYKkpKSgffv2wc0331ykU3nBczNu3Lhg5MiRQYsWLYK6desG++yzT/Dyyy9vdp8vvvhisPfeewf16tULdtppp+Cuu+4qdff1sWPHBm3atAmSk5ODDh06BPfff3+xt/3kk0+Cgw8+OKhfv36R56Sk9/r06dODAw44IEhJSQkaNGgQHHHEEcG7775b5JqCx/n555+LjBf3HthUbm5ucMsttwS9e/cOWrVqFSQnJwcpKSlBhw4dgiFDhgTLli0rcj0bdV8veH5L+tr4d1m4cGFw7rnnBjvuuGOQlJQUNG3aNDjooIOCm266qcTaJElFxYKglK03JUmS4sCiRYto27YtN998M4MHD466HEmStol7yiVJkiRJioihXJIkSZKkiLh8XZIkSZKkiDhTLkmSJElSRAzlkiRJkiRFxFAuSZIkSVJE6kRdQGXLz8/nxx9/JDU1lVgsFnU5kiRJkqQaLggCVq1aRfPmzUlI2PJceI0P5T/++CMtW7aMugxJkiRJUi3z3Xff0aJFiy1eU+NDeWpqKhA+GQ0bNoy4GkmSJElSTbdy5UpatmxZmEe3pMaH8oIl6w0bNjSUS5IkSZKqTGm2UNvoTZIkSZKkiBjKJUmSJEmKiKFckiRJkqSIRLqnfMyYMUybNo0vvviCevXqcdBBBzFu3Djat29feM3AgQN55JFHitzugAMO4P3336+wOoIgYP369eTl5VXYfarmSExMpE6dOh6pJ0mSJKnCRRrK33zzTS699FL2339/1q9fz/DhwznqqKOYP38+DRo0KLyuV69eTJkypfD7unXrVlgNa9euJSsri99//73C7lM1T/369cnMzKzQ954kSZIkRRrKZ86cWeT7KVOmkJ6ezscff8xhhx1WOJ6cnExGRkaFP35+fj4LFy4kMTGR5s2bU7duXWdDVUQQBKxdu5aff/6ZhQsXsssuu5CQ4K4PSZIkSRUjro5Ey87OBqBx48ZFxmfNmkV6ejqNGjWiW7dujBo1ivT09GLvIzc3l9zc3MLvV65cWeLjrV27lvz8fFq2bEn9+vUr4DdQTVSvXj2SkpJYvHgxa9euJSUlJeqSJEmSJNUQcTPlFwQB11xzDYcccgidOnUqHO/duzePPfYYb7zxBhMmTODDDz/k8MMPLxK8NzZmzBjS0tIKv1q2bLnVx3bmU1vje0SSJElSZYgFQRBEXQTApZdeyj/+8Q/eeecdWrRoUeJ1WVlZtG7dmieffJJ+/fpt9vPiZspbtmxJdnY2DRs2LHJtTk4OCxcupG3bts5+aot8r0iSJEkqrZUrV5KWllZsDt1UXCxfv/zyy/n73//OW2+9tcVADpCZmUnr1q356quviv15cnIyycnJlVGmJEmSJEkVKtI1uUEQcNlllzFt2jTeeOMN2rZtu9XbLFu2jO+++47MzMwqqFA33ngje++9d+H3AwcO5IQTTtim+6yI+5AkSZKkmiDSUH7ppZcydepUHn/8cVJTU1myZAlLlixhzZo1APz2228MHjyY2bNns2jRImbNmkWfPn3YYYcdOPHEE6MsPXIDBw4kFosRi8VISkpip512YvDgwaxevbpSH3fixIk8/PDDpbp20aJFxGIxPvnkk3LfhyRJkiTVZJEuX588eTIA3bt3LzI+ZcoUBg4cSGJiIp9++imPPvooK1asIDMzkx49evDUU0+RmpoaQcXxpeD89nXr1vH2229z/vnns3r16sLntcC6detISkqqkMdMS0uLi/uQJEmSpJog8uXrxX0NHDgQCI+ievnll1m6dClr165l8eLFPPzww6XqqL4NRcHq1VX/VY5+ewXnt7ds2ZIzzjiDM888k+nTpxcuOX/ooYfYaaedSE5OJggCsrOzufDCC0lPT6dhw4Ycfvjh/Pvf/y5yn2PHjqVZs2akpqZy3nnnkZOTU+Tnmy49z8/PZ9y4cey8884kJyfTqlUrRo0aBVC4HWGfffYhFosVfviy6X3k5uZyxRVXkJ6eTkpKCocccggffvhh4c9nzZpFLBbj9ddfZ7/99qN+/focdNBBfPnll2V+ziRJkiQpnnjO06Z+/x22267qv37/fZtLr1evHuvWrQPg66+/5umnn+a5554rXD5+7LHHsmTJEl588UU+/vhjOnfuzBFHHMHy5csBePrppxkxYgSjRo3io48+IjMzk0mTJm3xMYcOHcq4ceP44x//yPz583n88cdp1qwZAHPmzAHgtddeIysri2nTphV7H0OGDOG5557jkUceYe7cuey8884cffTRhXUVGD58OBMmTOCjjz6iTp06nHvuueV+riRJkiQpHsRF93Vtuzlz5vD4449zxBFHALB27Vr++te/0rRpUwDeeOMNPv30U5YuXVrYnf6WW25h+vTpPPvss1x44YXcfvvtnHvuuZx//vkA3HTTTbz22mubzZYXWLVqFRMnTuSuu+5iwIABALRr145DDjkEoPCxmzRpQkZGRrH3UbDc/uGHH6Z3794A3H///bz66qs8+OCDXHfddYXXjho1im7dugFwww03cOyxx5KTk+MRZZIkSZKqLUP5purXh99+i+Zxy+iFF15gu+22Y/369axbt46+ffty5513MmnSJFq3bl0YigE+/vhjfvvtN5o0aVLkPtasWcOCBQsA+Pzzz7n44ouL/Lxr167885//LPbxP//8c3Jzcws/CCiPBQsWsG7dOg4++ODCsaSkJLp06cLnn39e5No999yz8L8Luu8vXbqUVq1alfvxJUmSJClKhvJNxWLQoEHUVZRKjx49mDx5MklJSTRv3rxIM7cGm/wO+fn5ZGZmMmvWrM3up1GjRuV6/Hr16pXrdhsL/reXPhaLbTa+6djGv1/Bz/Lz87e5BkmSJEmKinvKq7EGDRqw884707p16612V+/cuTNLliyhTp067LzzzkW+dthhBwA6dOjA+++/X+R2m36/sV122YV69erx+uuvF/vzunXrApCXl1fifey8887UrVuXd955p3Bs3bp1fPTRR3To0GGLv5MkSZIkVXfOlNcSRx55JF27duWEE05g3LhxtG/fnh9//JEXX3yRE044gf32248rr7ySAQMGsN9++3HIIYfw2GOP8dlnn7HTTjsVe58pKSlcf/31DBkyhLp163LwwQfz888/89lnn3HeeeeRnp5OvXr1mDlzJi1atCAlJWWz49AaNGjAoEGDuO6662jcuDGtWrVi/Pjx/P7775x33nlV8dRIkiRJUmQM5bVELBbjxRdfZPjw4Zx77rn8/PPPZGRkcNhhhxV2Sz/11FNZsGAB119/PTk5OZx00kkMGjSIl19+ucT7/eMf/0idOnX405/+xI8//khmZmbhvvQ6depwxx138Oc//5k//elPHHroocUunx87diz5+fmcddZZrFq1iv3224+XX36Z7bffvlKeC0mSJEnVT15+wJyFy1m6Kof01BS6tG1MYkJs6zeMc7EgKMcB2dXIypUrSUtLIzs7m4YNGxb5WU5ODgsXLqRt27Z28NYW+V6RJEmSojNzXhYjZ8wnK3vDyVCZaSmM6NORXp0yI6yseFvKoZtyT7kkSZIkKW7NnJfFoKlziwRygCXZOQyaOpeZ87IiqqxiGMolSZIkSXEpLz9g5Iz5FLe8u2Bs5Iz55OVX3wXghnJJkiRJUlyas3D5ZjPkGwuArOwc5ixcXnVFVTBDuSRJkiQpLi1dVXIgL8918chQLkmSJEmKS+mppWuyXNrr4pGhXJIkSZIUl7q0bUxmWgolHXwWI+zC3qVt46osq0IZyiVJkiRJcSkxIcaIPh0BNgvmBd+P6NOxWp9XbiiXJEmSJMWtXp0ymdy/MxlpRZeoZ6SlMLl/57g8p7ws6kRdgCRJkiRJW9KrUyY9O2YwZ+Fylq7KIT01XLJenWfICxjKVWqxWIznn3+eE044IepSJEmSJNUyiQkxurZrEnUZFc7l69VMLBbb4tfAgQMr7bGzsrLo3bt3pd1/gY1/nwYNGrDLLrswcOBAPv744zLfV/fu3bnqqqsqvkhJkiRJqgCG8gqQlx8we8Ey/vbJD8xesIy8/KDSHisrK6vw6/bbb6dhw4ZFxiZOnFjk+nXr1lXYY2dkZJCcnFxh97clU6ZMISsri88++4y7776b3377jQMOOIBHH320Sh5fkiRJkqqCoXwbzZyXxSHj3uD0+9/nyic/4fT73+eQcW8wc15WpTxeRkZG4VdaWhqxWKzw+5ycHBo1asTTTz9N9+7dSUlJYerUqSxbtozTTz+dFi1aUL9+ffbYYw+eeOKJIvfbvXt3rrjiCoYMGULjxo3JyMjgxhtvLHJNLBZj+vTpACxatIhYLMa0adPo0aMH9evXZ6+99mL27NlFbnP//ffTsmVL6tevz4knnsitt95Ko0aNtvp7NmrUiIyMDNq0acNRRx3Fs88+y5lnnslll13Gr7/+CrDV32vgwIG8+eabTJw4sXDmfdGiReTl5XHeeefRtm1b6tWrR/v27Tf7MEOSJEmSqoKhfBvMnJfFoKlzycrOKTK+JDuHQVPnVlow35rrr7+eK664gs8//5yjjz6anJwc9t13X1544QXmzZvHhRdeyFlnncUHH3xQ5HaPPPIIDRo04IMPPmD8+PH8+c9/5tVXX93iYw0fPpzBgwfzySefsOuuu3L66aezfv16AN59910uvvhirrzySj755BN69uzJqFGjyv17XX311axataqwpq39XhMnTqRr165ccMEFhSsJWrZsSX5+Pi1atODpp59m/vz5/OlPf2LYsGE8/fTT5a5NkiRJksrDRm/llJcfMHLGfIpbqB4Qnpk3csZ8enbMqPKOgFdddRX9+vUrMjZ48ODC/7788suZOXMmzzzzDAcccEDh+J577smIESMA2GWXXbjrrrt4/fXX6dmzZ4mPNXjwYI499lgARo4cye67787XX3/Nbrvtxp133knv3r0LH3vXXXflvffe44UXXijX77XbbrsB4Sw9wI477rjF3ystLY26detSv359MjIyCq9LTExk5MiRhd+3bduW9957j6effppTTjmlXLVJkiRJUnk4U15OcxYu32yGfGMBkJWdw5yFy6uuqP/Zb7/9inyfl5fHqFGj2HPPPWnSpAnbbbcdr7zyCt9++22R6/bcc88i32dmZrJ06dItPtbGt8nMDM8HLLjNl19+SZcuXYpcv+n3ZREE4UcgsVj4IUdpf6/i3HPPPey33340bdqU7bbbjvvvv79Ut5MkSZKkiuRMeTktXVVyIC/PdRWpQYMGRb6fMGECt912G7fffjt77LEHDRo04KqrrmLt2rVFrktKSiryfSwWIz8/f4uPtfFtCsJywW2CICgcK1AQrMvj888/B8KZbSj977Wpp59+mquvvpoJEybQtWtXUlNTufnmmzdbzi9JkiRJlc1QXk7pqSkVel1levvtt+nbty/9+/cHwtD81Vdf0aFDh0p93N122405c+YUGfvoo4/KfX8F3eaPPPJIoHS/V926dcnLyytyP2+//TYHHXQQl1xySeHYggULyl2XJEmSJJWXy9fLqUvbxmSmpVDSbvEYkJmWQpe2jauyrGLtvPPOvPrqq7z33nt8/vnnXHTRRSxZsqTSH/fyyy/nxRdf5NZbb+Wrr77i3nvv5aWXXtps9rw4K1asYMmSJSxevJhXX32Vk08+mccff5zJkycXdm8vze/Vpk0bPvjgAxYtWsQvv/xCfn4+O++8Mx999BEvv/wy//3vf/njH//Ihx9+WBlPgSRJkiRtkaG8nBITYozo0xFgs2Be8P2IPh2rvMlbcf74xz/SuXNnjj76aLp3705GRgYnnHBCpT/uwQcfzD333MOtt97KXnvtxcyZM7n66qtJSdn66oFzzjmHzMxMdtttNwYNGsR2223HnDlzOOOMMwqvKc3vNXjwYBITE+nYsSNNmzbl22+/5eKLL6Zfv36ceuqpHHDAASxbtqzIrLkkSZIkVZVYsC2bfKuBlStXkpaWRnZ2Ng0bNizys5ycHBYuXEjbtm1LFRSLM3NeFiNnzC/S9C0zLYURfTrSq1PmNtVeE11wwQV88cUXvP3221GXUiYV8V6RJEmSVDtsKYduyj3l26hXp0x6dsxgzsLlLF2VQ3pquGQ9HmbI48Ett9xCz549adCgAS+99BKPPPIIkyZNirosSZIkSYoLhvIKkJgQo2u7JlGXEZfmzJnD+PHjWbVqFTvttBN33HEH559/ftRlSZIkSVJcMJSrUj399NNRlyBJkiRJcctGb5IkSZIkRcRQDtTwXneqAL5HJEmSJFWGWh3Kk5KSAPj9998jrkTxruA9UvCekSRJkqSKUKv3lCcmJtKoUSOWLl0KQP369YnF7JquDYIg4Pfff2fp0qU0atSIxMTEqEuSJEmSVIPU6lAOkJGRAVAYzKXiNGrUqPC9IkmSJEkVpdaH8lgsRmZmJunp6axbty7qchSHkpKSnCGXJEm1Xl5+wJyFy1m6Kof01BS6tG1MYoKrTKVtVetDeYHExESDlyRJklSMmfOyGDljPlnZOYVjmWkpjOjTkV6dMiOsTKr+anWjN0mSJElbNnNeFoOmzi0SyAGWZOcwaOpcZs7LiqgyqWYwlEuSJEkqVl5+wMgZ8ynucNiCsZEz5pOX7/GxUnkZyiVJkiQVa87C5ZvNkG8sALKyc5izcHnVFSXVMIZySZIkScVauqrkQF6e6yRtzlAuSZIkqVjpqSkVep2kzRnKJUmSJBWrS9vGZKalUNLBZzHCLuxd2jauyrKkGsVQLkmSJKlYiQkxRvTpCLBZMC/4fkSfjp5XLm0DQ7kkSZKkEvXqlMnk/p3JSCu6RD0jLYXJ/Tt7Trm0jepEXYAkSZKk+NarUyY9O2YwZ+Fylq7KIT01XLLuDLm07QzlkiRJkrYqMSFG13ZNoi5DqnFcvi5JkiRJUkQM5ZIkSZIkRcRQLkmSJElSRAzlkiRJkiRFxFAuSZIkSVJEDOWSJEmSJEXEI9EkSZIkqZTy8gPPa1eFMpRLkiRJUinMnJfFyBnzycrOKRzLTEthRJ+O9OqUGWFlqs5cvi5JkiRJWzFzXhaDps4tEsgBlmTnMGjqXGbOy4qoMlV3hnJJkiRJ2oK8/ICRM+YTFPOzgrGRM+aTl1/cFapQWVnwzTdRV1GhDOWSJEmStAVzFi7fbIZ8YwGQlZ3DnIXLq66o2mbhQhg0CNq0gWuuibqaCuWeckmSJEnagqWrSg7k5blOZTB/PowZA088AXl54diyZZCbC8nJ0dZWQZwplyRJkqQtSE9NqdDrVAoffQT9+sHuu8PUqWEgP/poePNNePvtGhPIwZlySZIkSdqiLm0bk5mWwpLsnGL3lceAjLTweDRtgyCAt96C0aPhlVc2jPfrB8OGwb77RldbJXKmXJIkSZK2IDEhxog+HYEwgG+s4PsRfTp6Xnl5BQG8+CIceih07x4G8sREOPts+OwzeO65GhvIwVAuSZIkSVvVq1Mmk/t3JiOt6BL1jLQUJvfv7Dnl5ZGXB888A507w7HHwrvvhsvSBw2Cr76CRx6Bjh2jrrLSuXxdkiRJkkqhV6dMenbMYM7C5SxdlUN6arhk3RnyMlq3Dh57DMaOhS+/DMcaNAjD+DXXQGbt+oDDUC5JkiRJpZSYEKNruyZRl1E9rVkDDz4IN98M334bjm2/PVxxBVx+OTSpnc+roVySJEmSVHlWroTJk+HWW2Hp0nCsWTO49lq4+GJITY22vogZyiVJkiRJFe+XX+COO+DOO2HFinCsdWu4/no45xxI8Qg5MJRLkiRJkirSDz/AhAlw773w++/h2G67wdChcPrpkJQUbX1xxlAuSZIkSdp233wD48bBww/D2rXhWOfO4RnjJ54ICR7+VRxDuSRJkiSp/ObNCzupP/EE5OeHY4ceGobxo4+GmN3pt8RQLkmSJEnlkJcf1O7j0T78EEaPhunTN4z16hWG8UMPjays6sZQLkmSJEllNHNeFiNnzCcrO6dwLDMthRF9OtKrUw0+ZzsI4M03YdQoeO21cCwWg5NOCveMd+4cbX3VkIv6JUmSJKkMZs7LYtDUuUUCOcCS7BwGTZ3LzHlZEVVWiYIAXngBDj4YevQIA3liIgwYAJ99Bs88YyAvJ0O5JEmSJJVSXn7AyBnzCYr5WcHYyBnzycsv7opqKC8PnnoK9tkH+vSB2bMhORkuuQS+/jps6tahQ9RVVmsuX5ckSZKkUpqzcPlmM+QbC4Cs7BzmLFxO13ZNqq6wirZ2LUydGjZw++qrcGy77cIwfvXVkJERbX01iKFckiRJkkpp6aqSA3l5ros7v/8ODzwAN98M338fjjVuDFdeCZddFv63KpShXJIkSZJKKT01pUKvixvZ2TBpEtx2G/z8cziWmQnXXgsXXRTOkqtSGMolSZIkqZS6tG1MZloKS7Jzit1XHgMy0sLj0aqFX36B22+Hu+4KgzlAmzZw/fUwcCCkVLMPF6ohG71JkiRJUiklJsQY0acjEAbwjRV8P6JPx/g/r/yHH8K94a1bh8ebZWdDx47w17+Ge8gvvthAXkUM5ZIkSZJUBr06ZTK5f2cy0oqG1oy0FCb37xzf55QvWAAXXght24Yz5L//DvvuC9OmwaefQv/+UMcF1VXJZ1uSJEmSyqhXp0x6dsxgzsLlLF2VQ3pquGQ9bmfI582DMWPgySchPz8c69YNhg6Fo46CWJzWXQsYyiVJkiSpHBITYvF/7NmcOTB6NPztbxvGjjkGhg2Dgw+Ori4VMpRLkiRJUk0SBDBrVhjGX3stHIvF4OSTw5nxffaJtDwVZSiXJEmSpJogCOAf/wgbt73/fjhWp064T/yGG6B9+2jrU7EM5ZIkSZJUneXlwTPPhHvG//OfcCw5Gc4/H667LuywrrhlKJckSZKk6mjt2vAIs7Fj4euvw7HUVLjkErjqKsjIiLQ8lY6hXJIkSZKqk99/hwcegJtvhu+/D8caNw6D+GWXwfbbR1qeysZQLkmSJEnVQXY2TJoEt90GP/8cjmVmwuDB4dnj220XbX0qF0O5JEmSJMWzn3+GiRPhrrvCYA7Qti1cfz0MGAApKdHWp21iKJckSZKkePT993DLLXDffbBmTTjWsWN4rNlpp4Wd1VXt+SpKkiRJUjz5+msYNw4eeQTWrQvH9tsPhg+H44+HhIRo61OFivTVHDNmDPvvvz+pqamkp6dzwgkn8OWXXxa5JggCbrzxRpo3b069evXo3r07n332WUQVS5IkSVIl+fRTOOOM8DzxBx4IA3m3bvDKKzBnDpxwgoG8Bor0FX3zzTe59NJLef/993n11VdZv349Rx11FKtXry68Zvz48dx6663cddddfPjhh2RkZNCzZ09WrVoVYeWSJEmSVEHefz+cAd9zT3jiCcjPh2OOgXfegVmzoGdPiMWirlKVJBYEQRB1EQV+/vln0tPTefPNNznssMMIgoDmzZtz1VVXcf311wOQm5tLs2bNGDduHBdddNFW73PlypWkpaWRnZ1Nw4YNK/tXkCRJkqStCwJ44w0YPTr8vxAG7z/8IdwzvvfekZanbVOWHBpXax+y/9dJsHHjxgAsXLiQJUuWcNRRRxVek5ycTLdu3XjvvfeKvY/c3FxWrlxZ5EuSJEmS4kJ+Pvz979C1Kxx5ZBjI69SBc86Bzz+Hp54ykNcycRPKgyDgmmuu4ZBDDqFTp04ALFmyBIBmzZoVubZZs2aFP9vUmDFjSEtLK/xq2bJl5RYuSZIkSVuzfn24NH3vvaFvX/jgg/Aos8suCxu7PfRQuJdctU7chPLLLruM//znPzzxxBOb/Sy2yf6JIAg2GyswdOhQsrOzC7++++67SqlXkiRJkrZq7dqwadtuu4VN3D79FFJTYcgQWLQI7rwTWreOukpFKC6ORLv88sv5+9//zltvvUWLFi0KxzMyMoBwxjwzM7NwfOnSpZvNnhdITk4mOTm5cguWJEmSpC1ZvToM4zffDD/8EI41aQJXXQWXXgrbbx9peYofkc6UB0HAZZddxrRp03jjjTdo27ZtkZ+3bduWjIwMXn311cKxtWvX8uabb3LQQQdVdbmSJEmStGUrVoTN29q0CQP4Dz9A8+Zw663hzPj//Z+BXEVEOlN+6aWX8vjjj/O3v/2N1NTUwn3iaWlp1KtXj1gsxlVXXcXo0aPZZZdd2GWXXRg9ejT169fnjDPOiLJ0SZIkSdpg6VK4/Xa4+24oaDa9005www1w9tngal6VINJQPnnyZAC6d+9eZHzKlCkMHDgQgCFDhrBmzRouueQSfv31Vw444ABeeeUVUlNTq7haSZIkSdrEd9/BLbfA/ffDmjXh2O67w7BhcMopYWd1aQvi6pzyyuA55ZIkSZIq3Fdfwbhx8OijsG5dOLb//jB8OPTpAwlx01NbEShLDvVjG0mSJEkqrf/8B8aMgaefDs8cB+jRI5wZP+IIKOGUKKkkhnJJkiRJ2prZs8MGbi+8sGHsuOPCMN61a3R1qdozlEuSJElScYIAXn89DOP//Gc4FouFe8WHDoW99oq2PtUIhnJJkiRJ2lh+PsyYEYbxOXPCsTp1wi7q118Pu+4abX2qUQzlkiRJkgSwfn24V3zMGJg3LxyrVw8uuACuvRZatYq2PtVIhnJJkiRJtVtuLjzySNhN/ZtvwrGGDeHSS+GqqyA9PdLyVLMZyiVJkiTVTqtXw333heeM//hjOLbDDmEQv/RSaNQoyupUSxjKJUmSJNUuv/4Kd98Nt98Oy5aFYzvuCIMHh0vVGzSItDzVLoZySZIkSbXDTz/BbbfBpEmwalU41q4d3HADnHUWJCdHW59qJUO5JEmSpJrt22/h5pvhgQcgJycc69QpPGP8D38IO6tLEfHdJ0mSJKlm+u9/YexY+Otfw87qAF26wPDhcNxxkJAQbX0ShnJJkiRJNc2//x2eMf7MMxAE4djhh4cz44cfDrFYtPVJGzGUS5IkSaoZZs+GUaPgH//YMHb88TB0KBx4YHR1SVtgKJckSZJUfQUBvPZaODM+a1Y4lpAAp54aNnDbc89Iy5O2xlAuSZIkqfrJz4e//z0M4x9+GI4lJcGAATBkCOyyS7T1SaVkKJckSZJUfaxfD089BWPGwGefhWP16sGFF4bnjLdoEW19UhkZyiVJkiTFv9xceOQRGDcOvvkmHGvYEC67DK66Cpo2jbQ8qbwM5ZIkSZLi1+rVcO+9MGEC/PhjOLbDDnD11XDppZCWFm190jYylEuSJEmKP7/+CnfdBRMnwrJl4diOO8J118EFF0D9+tHWJ1UQQ7kkSZKk+PHTT3DbbTBpEqxaFY7tvHPYSb1/f0hOjrY+qYIZyiVJkiRFb/FiuOUWeOAByMkJx/bYA4YNg5NPhjpGF9VMvrMlSZIkReeLL8LmbVOnhp3VAQ44AIYPh+OOg1gs2vqkSmYolyRJklT1/vWv8Izx556DIAjHjjginBnv0cMwrlrDUC5JkiSp6rzzThjGX3ppw9jxx4dh/IADoqtLioihXJIkSVLlCgJ45ZUwjL/1VjiWkACnngpDh4Z7x6VaylAuSZIkqXLk58P06WEY//jjcCwpCQYOhCFDwq7qUi1nKJckSZJUsdavhyeegDFj4PPPw7F69eCii+Daa6FFi2jrk+KIoVySJElSxcjJgYcfhvHjYeHCcKxhQ7j8crjySmjaNNLypHhkKJckSZK0bX77De69FyZMgKyscKxpU7jqKrj0UkhLi7Q8KZ4ZyiVJkiSVz/LlcOedcMcd4X9DuDR9yBA47zyoXz/a+qRqwFAuSZIkqWyWLIFbb4XJk8NZcoBddoEbboD+/aFu3Wjrk6oRQ7kkSZKk0lm8ONwv/uCDkJsbju25Z3jG+MknQ2JitPVJ1ZChXJIkSdKWffEFjB0Ljz0WdlYH6NoVhg+HY46BWCza+lQmefkBcxYuZ+mqHNJTU+jStjGJCb6GUTGUS5IkSSre3LnhGePTpkEQhGNHHhmG8W7dDOPV0Mx5WYycMZ+s7JzCscy0FEb06UivTpkRVlZ7JURdgCRJkqQ48/bb0Ls37LsvPPdcGMj79oUPPoBXX4Xu3Q3k1dDMeVkMmjq3SCAHWJKdw6Cpc5k5Lyuiymo3Q7kkSZKkMHjPnAmHHRZ+zZwJCQlwxhnw6acwfTp06RJ1lSqnvPyAkTPmExTzs4KxkTPmk5df3BWqTIZySZIkqTbLzw9nw/fbL5wdf/vtsHv6hRfCf/8b7iPv1CnqKrWN5ixcvtkM+cYCICs7hzkLl1ddUQLcUy5JkiTVTuvWwRNPwJgxYSM3CM8Vv/hiuOYa2HHHaOtThVq6quRAXp7rVHEM5ZIkSVJtkpMDU6aER5stWhSONWoEl10GV14JO+wQZXWqJOmpKRV6nSqOoVyVxqMWJEmS4siqVXDvvTBhAixZEo6lp4ez4oMGQcOG0danStWlbWMy01JYkp1T7L7yGJCRFv7NrqplKFel8KgFSZKkOLF8Odx5J0ycCL/+Go61bAlDhsB550G9etHWpyqRmBBjRJ+ODJo6lxgUCeYF02Yj+nR0Ei0CNnpThfOoBUmSpDiQlQXXXQetW8ONN4aBfNddw6XrX38dLlc3kNcqvTplMrl/ZzLSii5Rz0hLYXL/zk6eRcSZclWorR21ECM8aqFnxww/hZMkSaoMixaF+8Ufeghyc8OxvfeGYcOgXz9ITIyyOkWsV6dMenbMcJtpHDGUq0KV5aiFru2aVF1hkiRJNd3nn8PYseERZnl54dhBB8Hw4eFRZzFDl0KJCTH/Fo8jhnJVKI9akCRJqmJz58KoUfD88xD8b71iz55hGD/sMMO4FOcM5apQHrUgSZJURd5+OwzjL7+8YezEE8Nl6vvtF11dksrEUK4K5VELkiRJlSgIYOZMGD0a3nknHEtMhNNOg6FDYffdo61PUpnZfV0VquCoBdhwtEIBj1qQJEkqp7w8ePZZ2HdfOOaYMJDXrQsXXQT//S9MnWogl6opQ7kqnEctSJIkVZB16+CRR8LA/Yc/wL/+BQ0awLXXwsKFcM89sNNOUVcpaRu4fF2VwqMWJEmStsGaNeGRZuPHw7ffhmONGsEVV4RfTeycLdUUhnJVGo9akCRJKqNVq2DyZLj1Vvjpp3CsWTO45hq4+GJo2DDS8vLyAyddpApmKJckSZKitmwZ3HFH+LViRTjWujUMGQLnnAP16kVaHsDMeVmMnDGfrOwNR9tmpqUwok9HtydK28A95ZIkSVJUfvwRBg8OA/if/xwG8vbt4eGH4auv4JJL4iaQD5o6t0ggB1iSncOgqXOZOS8rosqk6s9QLkmSJFW1b74Jl6O3bQsTJsDq1bDPPvDMM/DZZzBgACQlRV0lEC5ZHzljfrHH3RaMjZwxn7z84q6QtDWGckmSJKmqzJ8PZ50Fu+4K994La9fCIYfASy/Bxx/DySeH547HkTkLl282Q76xAMjKzmHOwuVVV5RUg7inXJIkSapsH30Eo0fD889vGDv6aBg+HA49NLq6SmHpqpIDeXmuk1SUoVySJEmqDEEAb70VhvFXXtkw3q8fDBsG++4bXW1lkJ6aUqHXSSrKUC5JkiRVpCAIl6OPHg3vvhuOJSbCmWfC9ddDx47R1ldGXdo2JjMthSXZOcXuK48BGWnh8WiSys495ZIkSVJFyMsLG7V17gzHHhsG8uRkGDQo7KT+yCPVLpADJCbEGNEnrHvTE8kLvh/Rp6PnlUvlZCiXJEmStsW6deERZrvvDqecAp98Ag0ahEedLVwIkyaFXdarsV6dMpncvzMZaUWXqGekpTC5f2fPKZe2gcvXJUmSpPJYswYeegjGj4dvvw3HGjWCK6+Eyy+HJk0iLa+i9eqUSc+OGcxZuJylq3JITw2XrDtDLm0bQ7kkSZJUFitXwj33wK23wk8/hWPNmsG114Znj6emRltfJUpMiNG1Xc36sEGKmqFckiRJKo1ly2DiRLjzTlixIhxr3RqGDIFzzoF69SItT1L1ZCiXJEmStuTHH2HCBLj3Xli9OhzbbTcYOhROPx2SkqKtT1K1ZiiXJEmSivPNN+F+8SlTYO3acGyffWD4cDjhhPCYM0naRoZySZIkaWOffQZjx8ITT4THnAEcckgYxo8+GmI2NpNUcQzlkiRJEsCHH8Lo0TB9+oaxXr1g2DA49NDIypJUsxnKJUmSVHsFAbz5ZhjGX301HIvFoF+/cM/4vvtGW5+kGs9QLkmSpNonCODFF2HUKJg9OxxLTIQzz4QbboAOHaKtT1KtYSiXJElS7ZGXB889F86M//vf4VhyMpx7bni0WZs2kZYnqfYxlEuSJKnmW7sWHnssbOD23/+GY9ttB4MGwdVXQ2ZmtPVJqrUM5ZIkSaq51qyBBx6Am2+G774Lx7bfHq68Ei6/HBo3jrY+SbWeoVySJEk1z8qVMGkS3HYbLF0ajmVkwDXXwMUXQ2pqtPVJ0v8YyiVJklRz/PILTJwId94J2dnhWJs24X7xc86BlJRIy5OkTRnKJUmSVP398ANMmAD33gu//x6O7bZbeKzZ6adDUlK09UlSCQzlkiRJqr4WLIDx4+Hhh8NmbgCdO8Pw4XDCCZCQEGV1krRVhnJJkiRVP/PmwZgx8OSTkJ8fjh16aBjGjzoKYrFo65OkUjKUS5IkqfqYMyc8Y/xvf9sw1rt3uEz90EOjq0uSyslQLkmSpPgWBDBrVhjGX3stHIvF4KSTwjDeuXOk5UnStjCUS5IkKT4FAfzjHzBqFLz/fjiWmAj9+8MNN4SN3CSpmjOUS5IkKb7k5cEzz4R7xv/zn3AsORnOOw+uuy484kySaghDuSRJkuLD2rXw17/C2LHw9dfh2HbbwSWXwNVXQ0ZGtPVJUiUwlEuSpEjl5QfMWbicpatySE9NoUvbxiQm2Dm7Vvn9d3jgAbj5Zvj++3CscWO48kq4/HLYfvto65OkSmQolyRJkZk5L4uRM+aTlZ1TOJaZlsKIPh3p1SkzwspUJbKzYdIkuO02+PnncCwzE669Fi66KJwll6QazlAuSZIiMXNeFoOmziXYZHxJdg6Dps5lcv/OBvOa6uefYeJEuOuuMJgDtG0L118PAwZASkq09UlSFTKUS5KkKpeXHzByxvzNAjlAAMSAkTPm07NjhkvZa5Lvv4dbboH77oM1a8Kxjh3DY81OOw3q+KeppNonIeoCJElS7TNn4fIiS9Y3FQBZ2TnMWbi86opS5fn6a7jgAthpp3CGfM0a2G8/eP55+PTT8IgzA7mkWsp//SRJUpVbuqrkQF6e6xSnPv00PNbsqacgPz8c69YNhg+HI4+EmKsgJMlQLkmSqlx6aun2DJf2OsWZDz6AUaNgxowNY8ceGy5TP/jg6OqSpDhkKJckSVWuS9vGZKalsCQ7p9h95TEgIy08Hk3VRBDAG2/A6NHh/4VwJvwPfwjD+N57R1qeJMUr95RLkqQql5gQY0SfjkAYwDdW8P2IPh1t8lYd5OfD3/8OXbuGS9LfeCPcH37OOfD55+HSdQO5JJXIUC5JkiLRq1Mmk/t3JiOt6BL1jLQUj0OrDtavhyeeCAN3377hkvWUFLjsMliwAB56CNq3j7pKSYp7Ll+XJEmR6dUpk54dM5izcDlLV+WQnhouWXeGPI7l5sJf/wpjx4bhGyA1FS65BK6+Gpo1i7Y+SapmDOWSJClSiQkxurZrEnUZ2prVq+GBB+Dmm+GHH8KxJk3gqqvg0kth++0jLU+SqqtIl6+/9dZb9OnTh+bNmxOLxZg+fXqRnw8cOJBYLFbk68ADD4ymWEmSpNpoxYqwk3qbNmEA/+EHaN4cbr0VFi2C//s/A7kkbYNIZ8pXr17NXnvtxTnnnMNJJ51U7DW9evViypQphd/XrVu3qsqTJEmqvZYuhdtvh7vvhpUrw7GddoLrr4cBAyA5OdLyJKmmiDSU9+7dm969e2/xmuTkZDIyMqqoIkmSpFruu+/gllvg/vthzZpwbPfdw2PNTj017KwuSaowcf+v6qxZs0hPT6dRo0Z069aNUaNGkZ6eXuL1ubm55ObmFn6/suCTXUmSJJXsq6/C5m1//SusWxeO7b8/DB8OffpAgof2SFJliOt/XXv37s1jjz3GG2+8wYQJE/jwww85/PDDi4TuTY0ZM4a0tLTCr5YtW1ZhxZIkSdXMf/4Dp50Gu+0WHmO2bh306AGvvRYec9a3r4FckipRLAiCIOoiAGKxGM8//zwnnHBCiddkZWXRunVrnnzySfr161fsNcXNlLds2ZLs7GwaNmxY0WVLkhSpvPzA48RUPrNnw+jR8MILG8aOOw6GDYOuXaOrS5JqgJUrV5KWllaqHBr3y9c3lpmZSevWrfnqq69KvCY5OZlkG49IkmqBmfOyGDljPlnZOYVjmWkpjOjTkV6dMiOsTHErCOCNN8Ju6v/8ZziWkACnnAI33AB77RVtfZJUC1WrtUjLli3ju+++IzPTPzQkSbXbzHlZDJo6t0ggB1iSncOgqXOZOS8rosoUl/Lz4W9/gwMPhCOPDAN5UhKcdx588QU88YSBXJIiEulM+W+//cbXX39d+P3ChQv55JNPaNy4MY0bN+bGG2/kpJNOIjMzk0WLFjFs2DB22GEHTjzxxAirliQpWnn5ASNnzKe4/WcBEANGzphPz44ZLmWv7davh6efhjFjYN68cKxePbjgAhg8GOy9I0mRizSUf/TRR/To0aPw+2uuuQaAAQMGMHnyZD799FMeffRRVqxYQWZmJj169OCpp54iNTU1qpIlSYrcnIXLN5sh31gAZGXnMGfhcrq2a1J1hSl+5ObCo4/CuHGwYEE41rAhXHopXHUVbOEkG0lS1Yo0lHfv3p0t9Zl7+eWXq7AaSZKqh6WrSg7k5blONcjq1eH54rfcAj/8EI7tsEMYxC+9FBo1irI6SVIxqlWjN0mSBOmpKRV6nWqAFSvg7rvh9tvhl1/CsebN4brrwqXqDRpEWZ0kaQsM5ZIkVTNd2jYmMy2FJdk5xe4rjwEZaeHxaKrhli4Ng/jdd8PKleHYTjuFndTPPhs8kUaS4l616r4uSZIgMSHGiD4dgTCAb6zg+xF9OtrkrSb79lu44gpo3Tps4rZyJey+Ozz2GHz5ZTg7biCXpGrBUC5JUjXUq1Mmk/t3JiOt6BL1jLQUJvfv7DnlNdV//xseY9auHdx5J+TkQJcu4XFn//kPnHEG1HEhpCRVJ/6rLUlSNdWrUyY9O2YwZ+Fylq7KIT01XLLuDHkN9O9/hzPizzwTnjkO0KMHDB8Ohx8OMV9zSaquDOWSJFVjiQkxjz2ryWbPhtGj4YUXNoz16QNDh0LXrtHVJUmqMIZySZKkeBIE8PrrMGoUzJoVjiUkwCmnhGF8zz0jLU+SVLEM5ZIkSfEgPx/+/vdwZvzDD8OxpKSwi/r118Muu0RbnySpUhjKJUmSorR+PTz1VLhn/LPPwrF69eDCC+Haa6Fly2jrkyRVKkO5JElSFHJz4ZFHYNw4+OabcKxhQ7jsMrjySkhPj7Y+SVKVMJRLkiRVpd9+g/vugwkT4Mcfw7GmTeHqq+GSSyAtLdr6JElVylAuSZJUFX79Fe66CyZOhGXLwrEWLeC66+D886F+/WjrkyRFwlAuSZJUmX76CW67DSZNglWrwrGdd4YbboCzzoK6daOtT5IUKUO5JElSZVi8GG65BR54AHJywrE99oBhw+APf4DExGjrkyTFBUO5JElSRfrySxg7FqZODTurAxxwAAwfDscdB7FYtPVJkuKKoVySJKki/Otf4bFmzz4LQRCOHXFEODPeo4dhXJJULEO5pDLJyw+Ys3A5S1flkJ6aQpe2jUlM8A9NSbXYu+/C6NHw4osbxvr2haFDwxlySZK2wFAuqdRmzsti5Iz5ZGXnFI5lpqUwok9HenXKjLAySapiQQCvvgqjRsFbb4VjCQlw2mlhA7c99oi2PklStZEQdQGSqoeZ87IYNHVukUAOsCQ7h0FT5zJzXlZElUlSFcrPh+efhy5d4Oijw0CelAQXXBDuJX/sMQO5JKlMnCmXtFV5+QEjZ8wnKOZnARADRs6YT8+OGS5ll8rA7SDVyPr18MQT4Z7xzz8Px+rVg4sugmuvDc8blySpHAzlkrZqzsLlm82QbywAsrJzmLNwOV3bNam6wqRqzO0g1URODjz8MIwfDwsXhmNpaXD55XDFFdC0aaTlSZKqP0O5pK1auqrkQF6e66TarmA7yKarTwq2g0zu39lgHrXffoN774UJEyDrf9tzmjaFa66BQYPCYC5JUgUwlEvaqvTUlAq9TqrN3A4S55Yvh7vugokTw/8GaNkSrrsOzjsP6tePtj5JUo1jKJe0VV3aNiYzLYUl2TnFBokYkJEW7oeVtGVuB4lTS5bArbfC5MnhLDnALruEndT794e6daOtT5JUY9l9XdJWJSbEGNGnIxAG8I0VfD+iT0dn9aRScDtInFm8GC69FNq0gZtvDgP5nnvCk0+GDd3OPddALkmqVIZySaXSq1Mmk/t3JiOt6BL1jLQU979KZeB2kDjxxRcwcCDsvDNMmgS5udC1K7zwAnzyCZx6KiQmRl2lJKkWcPm6pFLr1SmTnh0zPMJJ2gZuB4nY3LkwejRMmwbB/16Bnj1h2DDo1g1i/nsmSapahnJJZZKYEHOfq7QNCraDDJo6lxgUCeZuB6lE77wDo0bBzJkbxvr2DcN4ly7R1SVJqvVcvi5JUhVzO0gVCYIwhB92GBx6aPjfCQlw5pnw6acwfbqBXJIUOWfKJUmKgNtBKlF+Pjz/fLhMfe7ccKxu3XAP+ZAh0K5dpOVJkrQxQ7kkSRFxO0gFW7cOnngCxowJG7lBeK74RRfBtdfCjjtGW58kScUwlEuSpOotJwemTIHx42HRonAsLQ0uvxyuvBJ22CHS8iRJ2hJDuSRJqp5WrYJ774UJE2DJknCsaVO45hq45BJo2DDa+iRJKgVDuSRJql6WL4c774SJE+HXX8Oxli3D/eLnnhsuWZckqZowlEuSpOohKwtuvRXuuQd++y0c23VXuOGGsKN63brR1idJUjkYyiVJUnxbtCjcL/7QQ5CbG47ttVd4xvhJJ0FiYqTlSZK0LQzlkiQpPn3+OYwdC489Bnl54VjXrjB8OBxzDMQ8Pk6SVP0ZyiVJUnyZOzc8Y3zaNAiCcKxnzzCMH3aYYVySVKMYyiVJUnx4+20YNQpefnnD2IknwtChsP/+0dUlSVIlMpRLkqToBEEYwkeNgnfeCccSE+G008Iwvvvu0dYnSVIlM5RLkqSql58fLk8fPRr+9a9wrG5dOOec8GiznXaKtj5JkqqIoVySJFWddevg8cfDBm5ffBGO1a8PF18M114LzZtHW58kSVXMUC5JkirfmjUwZUp4tNnixeFYo0ZwxRXhV5MmkZYnSVJUDOWSJKnyrFoF99wDEybATz+FY+np4az4xRdDw4bR1idJUsQM5ZIkqeItWwZ33AF33gm//hqOtWoV7hc/91yoVy/a+iRJihOGckmSVHF+/BFuvTWcHV+9Ohxr3x5uuAHOPBOSkqKtT5KkOGMolyRJ227hwnC/+EMPwdq14dg++8CwYeFZ44mJ0dYnSVKcMpRLqtby8gPmLFzO0lU5pKem0KVtYxITYlGXJdUe8+fDmDHwxBOQlxeOHXwwDB8OvXpBzP89SpK0JYZySdXWzHlZjJwxn6zsnMKxzLQURvTpSK9OmRFWJtUCH30UnjH+/PMbxo4+Ogzjhx4aXV2SJFUzCVEXIEnlMXNeFoOmzi0SyAGWZOcwaOpcZs7LiqgyqQYLAnjzzTB877//hkDer18Y0mfOrDGBPC8/YPaCZfztkx+YvWAZeflB1CVJkmooZ8ollVtUS8fz8gNGzphPcX8iB0AMGDljPj07ZriUXaoIQQAvvRTOjL/7bjiWmBg2brv+eujYMdr6KpircCRJVclQLqlcovyjdc7C5ZvNkG8sALKyc5izcDld2zWp1FqkGi0vD6ZNC8P4J5+EY8nJ4ZFm110HbdtGWl5lKFiFs+mHfgWrcCb372wwlyRVKJevSyqzqJeOL11VciAvz3WSNrFuHTz8MOy+O5xyShjIGzSAwYPDLuuTJtXIQL61VTgQrsJxKbskqSI5Uy79j128N9jScxEPS8fTU1Mq9DpJ/7NmDTz4INx8M3z7bTi2/fZwxRVw+eXQpGavPHEVjiQpCoZyCfcPbmxrz0U8/NHapW1jMtNSWJKdU+yHAzEgIy38MEFSKaxcCZMnw623wtKl4VizZnDttXDxxZCaGm19VcRVOJKkKLh8XbVe1Eux40lpnot4+KM1MSHGiD5hY6lN5+ILvh/Rp2OtXekgldqyZfCnP0Hr1nDDDWEgb906XJ6+aFG4b7yWBHJwFY4kKRqGctVq7h/coLTPxQ7bJZfq/ir7j9ZenTKZ3L8zGWlFHycjLcVGTNLW/PhjOAveujX85S+wYgXsths88gh89RUMGgQptS94FqzCKenjvBjhyiFX4UiSKpLL11WrxcNS7HhR2ueCgLhZOt6rUyY9O2bYC0AqrW++gfHjYcoUWLs2HNtnHxg+HE48ERJq92f1BatwBk2dSwyK/BvnKhxJUmWp3f/fV7VePCzFjhel/R1/WZ0bV0vHExNidG3XhL5770jXdk38Y1kqzmefwVlnwa67wr33hoH8kEPCs8c//hhOOqnWB/ICrsKRJFU1Z8pVq7l/cIOyPBdd2zVhcv/OmzWEy6ilzfGkuPXhhzBmDDz//IaxXr1g2DA49NDo6opzrsKRJFUlQ7lqNbt4b1DW58I/WqU4FQTw1lswahS8+mo4FotBv34wdCjsu2+09VUTBatwJEmqbK5VU61mF+8NyvNcuHRciiNBAC++GC5L7949DOSJiXD22eHy9WefNZBLkhSHDOWq9dw/uIHPhVQN5eXBM89A585w7LHw3nuQnBx2UP/qq7CjeocOUVcpSZJKEAuCoEaf9bRy5UrS0tLIzs6mYcOGUZejOJaXH7gU+398LqRqYO1aeOwxGDsW/vvfcGy77cIwfvXVkOmHaJIkRaUsOdQ95dL/uH9wA58LKY6tWQMPPgg33wzffhuObb89XHklXH45NK75PTAkSapJDOWSJFUHK1fCpElw222wdGk4lpEB114LF10EqanR1idJksrFUC5JUjz75ReYOBHuvBOys8OxNm1gyBA45xxIqflHNkqSVJOVq9HbW2+9xfr16zcbX79+PW+99dY2FyVJUq33ww9wzTXQujXcdFMYyDt0gEcfDfeQDxpkIJckqQYo10x5jx49yMrKIj09vch4dnY2PXr0IC8vr0KKkySp1lmwAMaPh4cfDpu5QdhZfdgwOPFESPDgFEmSapJyhfIgCIjFNu/EvGzZMho0aLDNRUmSVOvMmwdjxsCTT0J+fjh26KEwfDgcdRQU8/93JUlS9VemUN6vXz8AYrEYAwcOJDk5ufBneXl5/Oc//+Gggw6q2AolSarJ5syB0aPhb3/bMNa7NwwdGoZySZJUo5UplKelpQHhTHlqair16tUr/FndunU58MADueCCCyq2QkmSapoggFmzwjD+2mvhWCwGJ50UhvHOnSMtT5IkVZ0yhfIpU6YA0KZNGwYPHuxSdUmSyiII4B//gFGj4P33w7HEROjfH264AXbbLdr6JElSlYsFQRBEXURlWrlyJWlpaWRnZ9OwYcOoy5Ek1UZ5efDss+HM+H/+E44lJ8N558F114VHnEmSpBqjLDm01DPlnTt35vXXX2f77bdnn332KbbRW4G5c+eWvlpJkmqqtWth6lQYOxa++ioc2247uOQSuPpqyMiItj5JkhS5Uofyvn37FjZ269u37xZDuSRJtdrvv8ODD8LNN8N334VjjRvDlVfCZZeF/y1JkkQZQvn2229Pwv/ORj333HNp0aJF4feSJAnIzoZJk+C22+Dnn8OxzEy49lq46KJwllySJGkjpd5TXqdOHX788UfS09NJTEwkKyuL9PT0yq5vm7mnXJJU6X7+GSZOhLvuCoM5QNu2cP31MGAApKREW58kSapSlbKnvHnz5jz33HMcc8wxBEHA999/T05OTrHXtmrVqmwVS5JUHX3/PUyYAPfdFy5ZB+jYMTzW7LTToE6ZDjmRJEm1UKlnyu+77z4uv/xy1q9fX+I1QRAQi8XIy8ursAK3lTPlkqQK9/XXMG4cPPIIrFsXju27LwwfDn37gtu7JEmq1cqSQ8t0JNqqVatYvHgxe+65J6+99hpNmjQp9rq99tqrbBVXIkO5JKnCfPopjBkDTz0F+fnh2GGHhWG8Z0+wCaokSaKSlq8DpKam0qlTJ6ZMmcLBBx9c2I1dkqQa7YMPwjPG//73DWPHHAPDhsHBB0dXlyRJqvbKtdltwIABAHz88cd8/vnnxGIxOnToQOfOnSu0OEmSIhME8M9/hmH89dfDsVgMTj45DON77x1peZIkqWYoVyhfunQpp512GrNmzaJRo0YEQUB2djY9evTgySefpGnTphVdpyRJVSMI4IUXYNSocIYcwoZtZ50VdlNv3z7a+iRJUo1Srk40l19+OStXruSzzz5j+fLl/Prrr8ybN4+VK1dyxRVXVHSNkiRVvrw8ePJJ2GsvOP74MJCnpMBll4WN3R56yEAuSZIqXJkavRVIS0vjtddeY//99y8yPmfOHI466ihWrFhRUfVtMxu9SZK2aO1aePTRsJv611+HY6mpcMklcPXV0KxZtPVJkqRqp9IavRXIz88nKSlps/GkpCTyC7rRSpIUz1avhgcegFtuCc8bB2jcGK66Kpwd3377SMuTJEm1Q7mWrx9++OFceeWV/Pjjj4VjP/zwA1dffTVHHHFEhRUnSVKFW7EibN7Wpk0YwL//Hpo3h1tvhcWL4Y9/NJBLkqQqU66Z8rvuuou+ffvSpk0bWrZsSSwW49tvv2WPPfZg6tSpFV2jJEnb7uef4fbb4a67YOXKcGynncLmbQMGgMd8SpKkCJQrlLds2ZK5c+fy6quv8sUXXxAEAR07duTII4+s6PokSdo2330XLlG//35YsyYc69gxPNbs1FPDzuqSJEkRKfNfIuvXryclJYVPPvmEnj170rNnz8qoS5KkbfPVV2HztkcfhXXrwrH99oPhw8Pu6gnl2sFV4fLyA+YsXM7SVTmkp6bQpW1jEhNiUZclSZKqSJlDeZ06dWjdujV5eXmVUY8kSdvmP/+BMWPg6aehoPlo9+7hzPiRR0IsfgLvzHlZjJwxn6zsnMKxzLQURvTpSK9OmRFWJkmSqkq5pgn+7//+j6FDh7J8+fKKrkeSpPJ5/33o0yc8Z/zJJ8NAfuyx8O678M9/Qs+ecRfIB02dWySQAyzJzmHQ1LnMnJcVUWWSJKkqlSuU33HHHbz99ts0b96c9u3b07lz5yJfpfXWW2/Rp08fmjdvTiwWY/r06UV+HgQBN954I82bN6devXp0796dzz77rDwlS5JqoiCA116Dww+Hrl3hhRfC4H3qqfCvf4XfH3RQ1FVuJi8/YOSM+QTF/KxgbOSM+eTlF3eFJEmqScrV3eaEE04gFosRBNv2x8Lq1avZa6+9OOecczjppJM2+/n48eO59dZbefjhh9l111256aab6NmzJ19++SWpqanb9NiSpGosPx9mzAiPNpszJxyrUwfOPjvspr7rrtHWtxVzFi7fbIZ8YwGQlZ3DnIXL6dquSdUVJkmSqlyZQvnvv//Oddddx/Tp01m3bh1HHHEEd955JzvssEO5Hrx379707t272J8FQcDtt9/O8OHD6devHwCPPPIIzZo14/HHH+eiiy4q12NKkqqx9evDveJjxsC8eeFYSgpccAEMHgytWkVbXyktXVVyIC/PdZIkqfoq0/L1ESNG8PDDD3Psscdy+umn89prrzFo0KBKKWzhwoUsWbKEo446qnAsOTmZbt268d5775V4u9zcXFauXFnkS5JUzeXmwn33Qfv2cOaZYSBPTYUbboDFi+GOO6pNIAdIT02p0OskSVL1VaaZ8mnTpvHggw9y2mmnAXDmmWdy8MEHk5eXR2JiYoUWtmTJEgCaNWtWZLxZs2YsXry4xNuNGTOGkSNHVmgtkqSIrF4dhvFbboEffwzHmjSBq6+GSy+FRo0iLa+8urRtTGZaCkuyc4rdVx4DMtLC49EkSVLNVqaZ8u+++45DDz208PsuXbpQp04dfiz4Q6kSxDbplBsEwWZjGxs6dCjZ2dmFX999912l1SZJqiQrVsBNN0Hr1nDNNWEgb94cbrstnBkfPrzaBnKAxIQYI/p0BMIAvrGC70f06eh55ZIk1QJlminPy8ujbt26Re+gTh3Wr19foUUBZGRkAOGMeWbmhrNaly5dutns+caSk5NJTk6u8HokSVVg6dIweN99N6xaFY7ttFO4TP3ss6EG/fveq1Mmk/t33uyc8gzPKZckqVYpUygPgoCBAwcWCb05OTlcfPHFNGjQoHBs2rRp21xY27ZtycjI4NVXX2WfffYBYO3atbz55puMGzdum+9fkhRHvv02XKJ+//2Q87+AuvvuMGwYnHJK2Fm9BurVKZOeHTOYs3A5S1flkJ4aLll3hlySpNqjTH/lDBgwYLOx/v37l/vBf/vtN77++uvC7xcuXMgnn3xC48aNadWqFVdddRWjR49ml112YZdddmH06NHUr1+fM844o9yPKUmKI//9L4wbB48+GnZWB+jSJVyeftxxkFCmXVbVUmJCzGPPJEmqxWLBth42vg1mzZpFjx49NhsfMGAADz/8MEEQMHLkSO69915+/fVXDjjgAO6++246depU6sdYuXIlaWlpZGdn07Bhw4osX5JUXv/+d3is2TPPhGeOAxx+eDgzfvjhsIXeIZIkSfGuLDk00lBeFQzlkhRHZs+G0aPhhRc2jPXpA0OHQteu0dUlSZJUgcqSQ2vmJj1JUvwIAnj9dRg1CmbNCscSEsK94kOHwp57RlqeJElSlAzlkqTKkZ8PM2aEM+Nz5oRjSUkwYAAMGQK77BJtfZIkSXHAUC5Jqljr18NTT4V7xj/7LByrVw8uvBCuvRZatoy2PkmSpDhiKJckVYzcXHjkkbCb+jffhGMNG8Jll8GVV0J6erT1SZIkxSFDuSRp26xeDffdF54z/uOP4dgOO8DVV8Mll0CjRpGWJ0mSFM8M5ZKk8vn1V7jrLpg4EZYtC8d23BGuuw4uuADq14+2PkmSpGrAUF5L5OUHzFm4nKWrckhPTaFL28YkJngOsKRy+OknuO02mDQJVq0Kx9q1gxtugLPOguTkaOuTJEmqRgzltcDMeVmMnDGfrOycwrHMtBRG9OlIr06ZEVYmqVpZvDhcov7AA5Dzv39P9tgDhg2Dk0+GOv6/FEmSpLJKiLoAVa6Z87IYNHVukUAOsCQ7h0FT5zJzXlZElUmqNr78Es49F3beOVyunpMDBxwAf/sbfPIJnHaagbyayMsPmL1gGX/75AdmL1hGXn4QdUmSJNV6/hVVg+XlB4ycMZ/i/uQKgBgwcsZ8enbMcCm7pM198kl4xvizz0Lwv39JDj8chg+HHj0g5r8b1YmrpiRJik/OlNdgcxYu32yGfGMBkJWdw5yFy6uuKEnx79134dhjYZ994JlnwkB+/PEweza8/noYzA3k1YqrpiRJil+G8hps6aqSA3l5rpNUgwUBvPIKdOsGhxwCL74ICQlw+unw73+HS9UPPDDqKlUOW1s1BeGqKZeyS5IUDUN5DZaemlKh10mqgfLz4fnnoUsXOPpoeOstSEqC88+HL76Axx+HPfeMukptA1dNSZIU39xTXoN1aduYzLQUlmTnFDtDEgMy0sLj0STVMuvWwZNPwtixMH9+OFavHlx4IQweDC1aRFufKoyrpiRJim/OlNdgiQkxRvTpCIQBfGMF34/o09Emb1JtkpMD99wDu+4KZ58dBvKGDcPmbYsXw+23G8hrGFdNSZIU3wzlNVyvTplM7t+ZjLSif2xlpKUwuX9nO+5KtcVvv8GECbDTTjBoECxaBE2bht3Vv/0Wbrop/F41TsGqqZI+fo0RdmF31ZQkSdFw+Xot0KtTJj07ZjBn4XKWrsohPTX848sZcqkWWL4c7rwT7rgj/G8IZ8Kvuy7cN16/frT1qdIVrJoaNHUuMSiynclVU5IkRS8WBEGNbre6cuVK0tLSyM7OpmHDhlGXI0lVY8kSuPVWmDw5nCUH2HlnuOEGOOssqFs32vpU5TynXJKkqlOWHOpMuSTVJIsXw/jx8OCDkJsbju25JwwbBiefDImJ0danyLhqSpKk+GQol6Sa4Isvwk7qjz0G69eHYwceGDZwO/ZYiBm8FC5l79quSdRlSJKkjRjKJak6+9e/wmZtzz0HBbuRjjwynBnv3t0wLkmSFOcM5ZJUHb3zThjGX3ppw1jfvmEY79IlurokSZJUJoZySaouggBeeQVGjYK33w7HEhLg9NPDBm6dOkVbnyRJksrMUC5J8S4/H6ZPD2fGP/44HKtbFwYOhCFDoF27KKuTJEnSNjCUS1K8WrcOnnwSxoyBzz8Px+rXh4sugmuvhR13jLY+SZIkbTNDuSTFm5wcmDIlPNps0aJwLC0NLrsMrrwSmjaNtDxJkiRVHEO5JMWL336De+6BCRNgyZJwrGlTuOYauOQSaNgw2vokSZJU4QzlkhS15cvhzjth4kT49ddwrGVLuO46OO+8cMm6JEmSaiRDuSRFZcmScFb8nnvCWXKAXXYJO6n37x82c5MkSVKNZiiXpKq2aFG4X/yhhyA3Nxzba6/wjPGTToLExEjLkyRJUtUxlEtSVfn8cxg7Fh57DPLywrGuXWH4cDjmGIjFoq0vTuXlB8xZuJylq3JIT02hS9vGJCb4XEmSpJrBUC5Jle3jj8Mzxp9/HoIgHOvZM5wZ79bNML4FM+dlMXLGfLKycwrHMtNSGNGnI706ZUZYmSRJUsVIiLoASaqx3n4bevWC/faDadPCQN63L8yZA6+8At27G8i3YOa8LAZNnVskkAMsyc5h0NS5zJyXFVFlkiRJFcdQLkkVKQjgpZfg0EPhsMPg5ZfDPeL9+8O8eTB9Ouy/f9RVxr28/ICRM+YTFPOzgrGRM+aTl1/cFZIkSdWHy9clqSLk54ez4aNHw7/+FY7VrQvnnANDhsBOO5V4U/dMb27OwuWbzZBvLACysnOYs3A5Xds1qbrCJEmSKpihXJK2xbp18PjjYQO3L74Ix+rXh4svhmuvhebNt3hz90wXb+mqkgN5ea6TJEmKVy5fl6TyWLMGJk0KzxUfODAM5I0awR//CIsXh+ePlyKQu2e6eOmpKRV6nSRJUrxyplySymLVKpg8GW69FX76KRxLTw9nxS++GBo2LNXdbG3PdIxwz3TPjhm1cil7l7aNyUxLYUl2TrHPUQzISAuX+kuSJFVnzpRLUmksWwYjRkCrVnD99WEgb9UK7roLFi0K942XMpBD2fZM10aJCTFG9OkIhAF8YwXfj+jTsVZ+YCFJkmoWQ7kkbcmPP8LgwQStW8Of/wwrVrCmbTvyH3wIvv4aLr0U6tUr8926Z3rrenXKZHL/zmSkFV2inpGWwuT+nWv1nntJklRzuHxdkoqzcCGMHw8PPQRr1xIDPkvfibu7nsLMXbvS7KcGjPjyl3IHQ/dMl06vTpn07Jhhd3pJklRjGcolaWPz58OYMfDEE5CXB8CHO3bk7q6nMGunfSEWhsGCZmzlnbF1z3TpJSbEPPZMkiTVWIZySQL46KPwjPHnny8cCo46mktaH81L2+9SGMYLf8a2NWMr2DM9aOpcYv+7vwLumZYkSao93FMuqfYKAnjrLTj6aNh//w2BvF8/+PBD3p/0GC813nWzQF54c7atGZt7piVJkuRMuaTaJwhg5kwYNQrefTccS0yEM86AG26AjmHX76Wf/FCqu9uWZmzumZYkSardDOWSao+8PJg2LVym/skn4VjdunDuueGRZm3bFrm8qpqxuWdakiSp9jKUq9Ty8gNn82qZGvOar1sHjz0GY8fCl1+GYw0awMUXwzXXQPPmxd7MZmySJEmqbIZylcrMeVmMnDGfrOwNy3Qz01IY0aej+15rqBrxmq9ZEx5pNn48fPttOLb99nDFFXD55dBky7PTNmOTJElSZYsFQVDcBFCNsXLlStLS0sjOzqZhw4ZRl1MtzZyXxaCpczebKSyIITakqnmq/Wu+ciVMngy33QY//RSONWsG114bzo6nppbp7mrEBxSSJEmqMmXJoYZybVFefsAh494oEkY2VrB8953rD3e2sIao1q/5smUwcSLceSesWBGOtWoF118P55wD9eqV+65rzFJ+SZIkVbqy5FCXr2uL5ixcXmI4g6JHQtmoqmaolq/5jz/ChAlw772wenU41r49DB0adlRPStrmh6gtzdj88EGSJKlqGcq1RaU96mlbjoRSfKlWr/k334T7xadMgbVrw7F99oHhw+GEE8JjzlRqLtOXJEmqeglRF6D4VlVHQil+VIvX/LPP4KyzYNddw9nxtWvh0EPhpZfg44/hpJMM5GVU0Edg01USS7JzGDR1LjPnZUVUmSRJUs1mKNcWFRwJVdLi1RjhTJpHQtUccf2af/QR9OsHnTrB1KnhueO9esFbb4VfvXpBzKXWZZWXHzByxvxij30rGBs5Yz55+TW6BYkkSVIkDOXaooIjoYrjkVA108av+aavaiSveRDAm2/C0UfD/vvD88+Hwfukk8JZ8ZdeCmfJVW5l6SMgSZKkimUoV6mk1d+8UVaj+knxfzSWyqVXp0wm9+9MRlrRJeoZaSlV95oHAfzjH3DIIdC9O7zySrgk/eyzyft0HrPH3cvfEpoxe8EyZ3C3UbXqIyBJklTD2OhNW1TSedUAv/6+rsrrqQnK2926qrti9+qUSc+OGVXfiTsvD557DkaPhn//OxxLToZzz4UhQ5j5WzIj/z6frOyFhTepKc3Ioup8Xi36CEiSJNVQhnKVaEv7TCFcyjxyxnx6dsxw+Xoplbe7dVRdsav0GLC1a+Gxx2DsWPjvf8Ox7baDQYPg6qshM7PED4kKmpFVh5UbJQXvKDufF/QRWJKdU+z/3gvOprd3hCRJUsWLBUFQo9d9luXQdhU1e8EyTr///a1e98QFB9aK85u3VUmBsuDjjJICZXlvV22sWQMPPhgebfbdd+HY9tvDlVfC5ZdD4zAI5uUHHDLujRL3PhcEx3euPzxuPyQqKXgfv1cm9721MNLXuOB9BhSpo8a8zyRJkqpQWXKoe8pVonjZZ5qXHzB7wTL+9skP1Xb/cHm7W9fortgrV4az4m3ahOH7u+8gIwNuvhkWL4YRIwoDOVT/ZmQlHTmWlZ3DvcUEcqja1zgu+ghIkiTVQi5fV4niYZ9plEt6K1JZAuXGqw7Ke7utiWrvMgC//AITJ8Kdd0J2djjWpg0MGQLnnAMpxb+f4uVDovLY2laQLSnva1wekfURkCRJqsUM5SpR1PtMa8L+4QLlDZSVEUQj+6Djhx9gwgS49174/fdwbLfdYOhQOP10SNq8w//G4uFDovLa2ocrpVFVHzZUaR8BSZIkuXxdJYvyvOqatmy7vIGyooNoSUuoCz7omDkvq1T3UyYLFsBFF8FOO8Ftt4WBvHNnePZZ+OwzOPvsrQZy2PAhUUnvthjhhwvx2IysIgJ1PH7YIEmSpG1nKNcWRbXPtLrvH95UeQNlRQbRKv+gY948OPNM2HVXuO++sLv6oYfCSy/BRx/BSSdBQun/CYryQ6JttS2BOp4/bJAkSdK2c/m6tiqKfabVef9wcQoC5aCpc4lRfHfr4gJleW9XnMran775A80Jzxj/2982jPXqBcOGhaF8GxR8SLTp8vuMOO8zsLWtIAW29TWWJElS9WMoV6lU9T7T6rx/uCTlDZQVFUQr9YOOIIBZs8Iw/tpr4VgsFs6GDx0aLlevINWxGVlpPly58LC2/P3fWdXqwwZJkiRtO0O54lLUTeYqS3kDZUUE0Ur5oCMI4B//gFGj4P3/nWmfmAj9+8MNN4SN3CpBdWxGVpoPV4b06lCtPmyQJEnStosFQVA9OmWVU1kObVd8KWhKBsXPLFan7uvxIC8/4JBxb2z1g453rj9860EwLw+eeQbGjIH//CccS06G88+HwYPDI85UrEiPo5MkSVKVKEsOtdGb4lZUTeZqqgpplLZ2LTz4YDgDfvrpYSDfbju47jpYtAjuustAvhUFs/x9996Rru2aGMglSZJqOWfKFfecWaxY5Tqn/Pff4YEH4Oab4fvvw7HGjeHKK+Hyy2H77augckmSJKl6KEsONZRLtVCpP+jIzoZJk8LzxX/+ORzLzIRrrw3PHt9uu6otXJIkSaoGypJDbfQm1UJbbZT2888wcWK4HD07Oxxr0wauvx4GDoSU6tP1XpIkSYpnhnJVSy5pryTffw8TJsB994VL1gE6dAiPNTv9dKjjPxmSJElSRfIvbFU75doTrS37+msYNw4eeQTWrQvH9t0Xhg2DE06ABHtCSpIkSZXBv7RVrRQck7ZxIAdYkp3DoKlzmTkvK6LKqqlPP4UzzoD27cNGbuvWwWGHwcsvw4cfQr9+BnJJkiSpEvnXtqqNvPyAkTPmF3vGdsHYyBnzycuv0b0LK8YHH0DfvrDnnvDEE5CfD717w9tvw5tvwlFHQcztAJIkSVJlM5Sr2pizcPlmM+QbC4Cs7BzmLFxedUVVJ0EAb7wBRxwBBx4If/97GLz/8AeYOxdefBEOOSTqKiVJkqRaxT3lqjaWrio5kJfnulojPx9eeAFGjw5nyCFs2Na/P9xwA7RvHzbOW7DMxnmSJElSFTOUxwm7iW9demrpjuEq7XU1Xl4ePP00jBkT7h0HSE6G88+H666D1q0BG+dJkiRJUTKUxwFDUel0aduYzLQUlmTnFLuvPAZkpIUfaNRqubnw17+G3dS//jocS02FSy6Bq66CjIzCSwsa5236fBY0zpvcv7PvQUmSJKkSuac8YnYTL73EhBgj+nQEwgC+sYLvR/TpWHtXGKxeDRMnQrt2cMEFYSBv3Bj+/GdYvBjGji0SyG2cJ0mSJEXPUB4hQ1HZ9eqUyeT+nclIK7pEPSMtpfbO6q5YEe4Xb9MmnAn/4QfIzIQJE8Iw/sc/wvbbb3YzG+dJkiRJ0XP5eoTKEoq6tmtSdYVFpLT76nt1yqRnxwz34P/8M9x+O9x1F6xcGY61bQvXXw8DB4b7x7fAxnmSJElS9AzlETIUbVDWffWJCbFa8UFFsb7/Hm65Be67D9asCcc6doRhw+DUU8PO6qVg4zxJkiQpeobyCBmKQvHYbCwuu+F//XXYvO2RR2DdunBsv/1g+HA4/nhIKNtulNrWOC8uX1OViq+dJEmqyQzlEaptoag4W9tXHyPcV9+zY9igrCr+MI+7bviffhoea/bUU+GZ4wDdu4cz40ceCbHyPQcFjfMGTZ1LDIq8BjWtcV7cvaYqNV87SZJU08WCIKjRXcRWrlxJWloa2dnZNGzYMOpyNlMwSwzFh6Ka3rxs9oJlnH7/+1u97uojd+XJD7+t9D/MS5q1j+T1eP/9sIHbjBkbxo45JpwZP+igCnuYmh564uo1VZn42kmSpOqqLDnUUB4Hanoo2pK/ffIDVz75SbluW9F/mOflBxwy7o0Sm+8VrFx45/rDK2/2OAjgjTfCMP7GG/974Bj84Q8wdCjsvXelPGxNXR4cF6+pysXXTpIkVWdlyaEuX48Dtbmb+Lbsl990efu2Pl+RdsPPz4cXXoBRo2DOnHCsTh04++ywm/quu1bs422ipjbO84SD6svXTpIk1RaG8jhRU0PR1ixblbtNt6/IP8wj6Ya/fj0880w4Mz5vXjiWkgIXXACDB0OrVhX3WHGgqmfkPeGg+vK1kyRJtYWhPI7U1CXEJZk5L4vLnvxXhdxXRfxhXqXd8HNz4dFHw27qCxaEY6mpcOmlcPXVkJ6+7Y8RZ6LYpuEJB9WXr50kSaotDOVxorbtKy/oul5RKuIP8yrphr96Ndx/f3jO+A8/hGNNmoRB/NJLoVGj8t93HIvq2DtPOKi+fO0kSVJtUbaDjVUpCgLLpvsnCwLLzHlZEVVWeba2X7S0YoQfXlTEH+YFR4QV3O+mjwPbcETYihXhfvE2bcIA/sMP0Lw5+bfeygf/nMvfjh3I7GV55OXXvL6LWzv2DsK+AJXxu1fqa6pK5WsnSZJqC0N5xKIMLFEqy3LzRvWTiFE1f5j36pTJ5P6dyUgrOvOekZZSvtncpUvD88Rbt4b/+z/45RfYaSe47z5envEuB+fsyamPfcqVT37C6fe/zyHj3qhxH8KUpWFXZajw11RVxtdOkiTVBnG9fP3GG29k5MiRRcaaNWvGkiVLIqqo4tXWDsNlWW4+tt8eAJst78+opOX9FdIN/9tvwyXq998POf+reffdw4B+yinM/OLnSJZzRyEeGnbV5hMOqjtfO0mSVNPFdSgH2H333XnttdcKv09MTIywmooXD4ElClvbLwqQEIO7Tt+nMJxW5R/mW+qGv8WGfP/9b9i87dFHw87qAF26wPDhcNxxkJCw1dURFXnMWzyIl4ZdtfWEg5rA106SJNVkcR/K69SpQ0ZGRqmvz83NJTd3wzFbK1eurIyyKky8BJaqVrBfdNDUucSg2IB61+mdOWbPzCK3ifoP85Ia8t2ya8DBzzwQHm+Wnx/+oEePMIwffjjENoTr2rY6woZdkiRJUsnifk/5V199RfPmzWnbti2nnXYa33zzzRavHzNmDGlpaYVfLVu2rKJKy6cgsJQ0H1qRjcziTUn7RTPTUrinf9FAHg+Ka8jX+YfPuenBoRx8ck946qkwkPfpA++9B2+8AUccUSSQQ+1bHWHDLkmSJKlksSAI4raD2EsvvcTvv//Orrvuyk8//cRNN93EF198wWeffUaTJsXPIBY3U96yZUuys7Np2LBhVZVeJgVhD4rOGBdElJq0v7g41eF89rz8gEPGvREG8iDg4MX/5rLZT9H120/Dn8cSeH2PbhzxyG0k7r3XFu9r9oJlnH7/+1t9zCcuOLBGzJQXqG3H/kmSJKn2WrlyJWlpaaXKoXEdyje1evVq2rVrx5AhQ7jmmmtKdZuyPBlRMrDEt9kLlnHGfe/R86sPuOT9p9k76ysA1ibUYVqnw7nngJNY1HjHUgXpgoC/teXc71x/eNx9OLGtqsMHMJIkSdK2KksOjfs95Rtr0KABe+yxB1999VXUpVQ4OwzHsfXrqfvU48x86Bba//ItAGvqJPPkXkdxX5d+ZDVsWnhpaZacb2k/fU1fzh0PfQEkSZKkeFKtQnlubi6ff/45hx56aNSlVIqqDizOWm5Fbi488giMG8e+/+tlsLJufR7d9zim7Hs8yxo02uwmpW3IV7CfvqqOeZMkSZIUn+I6lA8ePJg+ffrQqlUrli5dyk033cTKlSsZMGBA1KVVey6X34LVq+G++8Jzxn/8EYBghx24t/PxTOpwFCtTttvsJuXpIF4RqyP8YEWSJEmq3uI6lH///fecfvrp/PLLLzRt2pQDDzyQ999/n9atW0ddWrVW0Fhu0/3MS7JzGDR1bo1vLFeiX3+Fu+6CiRNh2bJwbMcd4brriJ1/Pm0WrmRVBS8535bVEX6wIkmSJFV/1arRW3lUl0ZvVaVIF/Fi1OQmYyX66Se47TaYNAlWrQrHdt4ZbrgB+veH5OTCS+MlCJf0wUpt6dgvSZIkxbMa2+hN227OwuUlBnIIZ4CzsnOYs3B5zW/ItXhxuET9gQcg53/PyR57wLBhcPLJUGfz/3nEQ0O+vPyAkTPmF9u5PSAM5iNnzKdnx4za88GKJEmSVE0ZymuZ0nQHL8t11dKXX8LYsTB1KqxfH44dcAAMHw7HHQexLQfZ8iw5r8i9336wIkmSJNUchvJaprTdwUt7XbXyr3/BmDHw7LNQsGvjiCPCmfEePbYaxsuqIIi/Nn8Jz3/yA8tXryv82aZL3ssS2v1gRZIkSao5DOU13KZhb9/W25OZlsKS7Jxilz+Xp4t43Hv3XRg9Gl58ccPY8ceHYfyAAyrlIYvbe76xjZvqAWXapx71Byt2fJckSZIqjqG8BiupKdnxe2Vy31sLK7SLeNwJAnj1VRg1Ct56KxxLSIBTT4WhQ8O945WkpCZsRcojfL5vmPYp2b+vK1Mn/C5tG0f2wUq8NLqTJEmSaoqEqAtQ5SgIhpvO1C7JzuG+txZy4WFtyUgrOpOakZZS/bt25+fD889Dly5w9NFhIE9KgvPPD/eSP/54pQbyLTVh21QArCgmkBf8DMIZ9Lz8olckJsQY0acjsOGDlAKV+cHKlt5Tg6bOZea8rAp9PEmSJKk2cKa8BipNd+6//zuLN6/rwceLf60Zy5DXr4cnnwz3jM+fH47VqwcXXgjXXgstW1ZJGVtrwlYWW2rY1qtTJpP7d95s1jpjG/aqb4kd3yVJkqTKYSivgUrbnfvjxb9W/+7cOTnwyCMwbhwsXBiONWwIl18OV14JTZtWaTmV0VytpPvc2vFsFbnU3I7vkiRJUuUwlNdAtaI792+/wX33heeMZ/1v2XTTpnD11XDJJZCWVuUl5eUH/LIqt8Lvd0sN20o6nq2kfe1b2qu+JbXiPSVJkiRFwFBeA0XdnbtS/for3HUXTJwIy5aFYy1awHXXhfvG69ePpKytdVsvSaP6ScU2eoPyN2yrjKXm1eU9ZWd4SZIkVTeG8hooyu7cleann+DWW2HSpHCWHGDnneGGG+Css6Bu3VLfVUUHt9J0W99UwTJygEFT51ZoJ/zKWGpeHd5TdoaXJElSdWQor4EKunNXdNiLxOLFcPPN8OCD4f5xCLunDxsGf/gDJCaW6e4qOriVpdt6kwZ16bt3c47YrRnE4JffcklPTeHuMzrzl39suWFbWVTGUvN4f09V9HJ9SZIkqaoYyuNIRc7glrY7d9z68ksYOxamTg07qwMceCAMHw7HHguxsj8vlRHcSttt/Y/HdmDgwW15df4SBj/7780+FPjjsR3YvkFyhbz2lbXUPF7fU3aGlyRJUnVmKI8TlbH0dmvduePSv/4VHmv27LMQ/C9mHXlkODPevXu5wjhUXnAr7WzzDqnJvDp/SYkfClz6+L+Y3L8zfffesdSPXZLKXGoej+8pO8NLkiSpOkuIugBtmMHdNFgUzODOnJdV7vsu6M7dd+8d6dquSfwG8nffhWOOgc6d4ZlnwkDety+8/z68+ir06FHuQA5lC25lUdrZ5h0aJG/xQwEIPxTIyy/LzvTiFSw1hw1LywtUxFLzeHtP2RlekiRJ1ZmhPGJbm8GFigtrcScI4OWXoVs3OOQQeOklSEiAM86A//wHpk+HAw6okIeqrOBWMCtdUiyNEa54IEalfChQkoKl5hlpRT80yEhLqXH7q6tLZ3hJkiSpOC5fj1itXHqbnx8G7tGj4eOPw7GkJBg4EK6/Htq1C/fXL1hWYUukKyu4lbYB2i+/le788oqczY3HpeaVoTp0hpckSZJKYiiPWK1aertuHTz5ZLhn/PPPw7H69eGii+Caa8Lzxqmc/fWVvc96aw3QZi9YVqr7qujZ3IKl5jVZvHeGlyRJkrbEUB6xipzBrejztytMTg5MmQLjx8OiReFYWhpcdhlceSU0bVp4aWUdbVXZwW1rs9LO5laueO0ML0mSJG1NLAiCGrhZeYOVK1eSlpZGdnY2DRs2jLqczeTlBxwy7o2thrV3rj98i4GxMmaXt9lvv8E998CECbBkSTjWtGk4Kz5oUBjMN1LwXJS0nL+0z8WWRPk8FXzgAMV/KFDT9npHIW4/mJIkSVKtUpYcaiiPA9sa1kqaXY4s7C1fDnfeCRMnwq+/hmMtW8J118F554VL1osxe8EyTr///a3e/RMXHLhNS7KjDG5x+eGJJEmSpApVlhzq8vU4sC1Lb8t7/nalBNOsLLjtNpg8OZwlB9h1V7jhBjjzTKhbd4s3r6r99VHus64tzdckSZIklY6hPE6UN6yVp3t7hc/WLloU7hd/6CHI/V+X8b32gmHD4KSTIDGxVHdTnY622pYPNWpD8zVJkiRJpWMojyPlCWtlnV2u0EZqn38OY8fCY49BXl441rUrDB8OxxwDsbLN/laXZmguQZckSZJUURKiLkDbpiyzy1tb6g4w/Pl5PD/3e2YvWEZefgntBubOhZNPht13h0cfDQN5z54waxa8+y4ce2yZAzls6JAOG/bDF4iXo60KPtTYdHVCwYcaM+dlRVSZJEmSpOrIUF7NFcwulxRTY4SzuF3aNi7VUvdlq9dy9dP/5vT73+eQcW8UDZlvvw29esG++8Jzz0EQwIknwpw58Mor0K1bucL4xgr212ekFf2wISMtJfLu5KX5UGPkjPklf5ghSZIkSZtw+Xo1V5bzt8vaIC0rO4eL//oxz7ZdyX6PTYZ33vnfgybCaafB0KHhbHkplXYfdrw2QyvP/n1JkiRJ2hJDeQ1Q2u7tZWmQFgvy6fXle1z6/jN0+mlBOFi3LpxzDgwZAjvtVKYay7oPOx6boVVVd3hJkiRJtYehvIYozezyr6tzSYjBllZX18lbT9/5bzLo/WfYefn3APyelMwXx59B5ztugubNy1xbhTaXi1B16g4vSZIkqXowlNcgW5pdnjkvi0sf/1ex+6EBktfl8odPX+PiD56jxcqlAGQnN+Dhffvw8L59CHbYgY8zMind4WYblPcc9XhUXbrDS5IkSao+DOW1wJaCcYPc3znzk5e44MPnabp6BQA/12/EA11O4LG9j+G35Prhhb+vK9de6Zq0D7ss+/clSZIkqTQM5bVAccG40ZqVnPPRDAbMnUGjnN8A+L5hU+494CSe3qMnuUnJm91PefZK17R92KXdvy9JkiRJpWEorwU2DrxNf1vOBXOe58xPXqLBunB8QeMWTD7wZKZ37M76xJLfEuXZK10T92HHa3d4SZIkSdWPoTyOlPbIsLJKT02hxYolXPzBc/zh01dJzlsPwGfpO3FX11N4edeu5CeUvFt8W/ZK19R92PHYHV6SJElS9WMojxNlPTKs1ObP54AxY5n1+GPUyc8H4MMdO3J311OYtdO+EIsRA7avn8Svv6/b7ObbulfafdiSJEmSVLJYEARbOCCr+lu5ciVpaWlkZ2fTsGHDqMspVklHhhXE1HIdGfbRRzB6NDz/fOHQm207M6nrKXzQYneIxTZ7DKByPhigEj90kCRJkqQ4U5Yc6kx5xCr0yLAggLfeCsP4K69sGO/XD4YOZU3Kjnw7Yz5soUFZZe2Vdh+2JEmSJG3OUB6xCjkyLAjgpZfCMP7uu+FYYiKccQbccAN07AhAL7Yeuitzr7T7sCVJkiSpKEN5xLbpyLC8PJg2LQzjn3wSjtWtC+eeC9ddBzvttNlNDMaSJEmSFD8M5REr15Fh69bBY4/B2LHw5ZfhWIMGcPHFcM010Lx5JVQqSZIkSapohvKIlenIsDVr4KGHYPx4+Pbb8IJGjeCKK8KvJtVrBryyjoCTJEmSpOrCUB6x0hwZ9ufDW5F4y81w663w00/hYLNmcO214ex4amqF11XZgdlu7JIkSZLkkWhxo7iQ2j4pl7t/eYedn5wCK1aEg61awfXXwznnQL16VVZLRQbmSjkCTpIkSZLiRFlyqKE8jhTMTq9csIg9nnqQzKceJbZ6dfjD9u1h6NCwo3pSUqXVUNmBOS8/4JBxb5TYcb5guf471x/uUnZJkiRJ1ZLnlFdTiYsW0vXm8TBlCqxdGw7usw8MGwYnnhgec1aJKvTM9BJUyBFwkiT9f3v3Hhx1df9//LUhIQs0WUg07KZcjGiBNBQFRCIIXgYIrYiXVrxRqNpvy3jHGy3tBPz9+hWvtdYalUGkQ62dFrBSbCwKQS1gQIISQ0ExilM3pgZMUphATM73j3S3bLLXXPbz2c3zMcNMsns+nz3h5JOd157zOW8AAJIEodwOWlralqO/8ELb15I0ZYq0ZIk0c6bkiM+McTwCc5dKwAEAAABAkiGU20GfPlJjY1sgnzmzLYyff37cuxGPwNypEnAAAAAAkKRSrO4A/uN//1fatUsqLbUkkEvxCcy+EnCh5v4dattUbmJeVqdfAwAAAAASBaHcLkaPlsaPt7QL8QjMvhJwvvO1P78kFc/OZ5M3AAAAAL0CodyGWlqNth+s05/3/FPbD9appTU+G+THKzAXFXhUcv04uV2BM+5ul5NyaAAAAAB6FUqi2UwsNcJ9JdRqG5uUk9E2g90dM8w9Xafcp6f6DwAAAABWok75SRIplMdSI7yngzOBGQAAAAA6h1B+kkQJ5S2tRlMe3ByyJJlDbcu737rvIm2qqok6vAMAAAAA4iuWHEpJNJuItkb4joN1WrahqkMg97VxSFq2oUrT890xz2y3nx0fP3yQ3vnkSMBsua+vzKADAAAAQNcRym0i2trf2z/6IqrwXl59WIUjsqN+/WDL4VMc0sl7zA3snyZJ+vJYs/+xnrjXHAAAAAB6C3Zft4noa39HNysdbciX/nsve/uw337T9y+PNQcEckmqqW/SwjW7VVrpjfr1AAAAAABtCOU2EW2N8HOjrBF+ytfSo2rX0mpCLoePhu+4ZRuq4la6DQAAAACSBaHcJqKtEZ7iiPL+7SjzcaR72aN9Kd+SeQAAAABA9AjlNlJU4FHJ9ePkdgUuZXe7nP4d1b84ejyqc0XbLpZl7vE8FwAAAAD0Bmz0ZjNFBR5Nz3eH3OE82nvPu7tdvM8FAAAAAL0BodyG+qQ4Qu6c7rv3vKa+KegKdV8984lR3nse6XzRiPU1AQAAAABtWL6eYKK99zza2uHhzheNzrwmAAAAAKANoTwBRXPveXecr33GHtQ/zV+rvKuv2V5Lq9H2g3X6855/avvBOnZyBwAAANArOIwxSZ1+Ghoa5HK5VF9fr8zMTKu7061aWk3Ie8+743zjhw/SO58cCTi/pG59TamtTvqyDVUBu8B7XE4Vz87vctgHAAAAgHiLJYcSymGp0kqvFq7Z3eF+dl/M745ZeAAAAACIp1hyKMvX0S06s/y8pdVo2YaqoBvM+R5btqGKpewAAAAAkha7r6PLOrv8vLz6cMAx7RlJ3vomlVcfDrkbPQAAAAAkMmbK0SW+5eftw3VNfZMWrtmt0kpvyGNrG0MH8s60AwAAAIBEQyhHp3V1+XlOhjPo451tBwAAAACJhlCOTotl+XkwE/Oy5HE5Q9ZHd6htGbxv13cAAAAASDaEcnRaV5ef90lxqHh2viR1COa+74tn53e55FpnUTsdAAAAQE9jo7dewFd/vKahSYf/fVxZA/rK7erX5Rrj3bH8vKjAo5Lrx3XYKM5tcZ1yaqcDAAAAiAdCeZILFi59uhoyfcvPQy1hd6gtXEdafl5U4NH0fLfKqw+rtrFJORnOLn9g0BWhaqf7Nq+jdjoAAACA7sLy9SQWamd0H28UO6SH0yfFoUvHhg+n0S4/75PiUOGIbM056+sqHJFt6ZJ1aqcDAAAAiBdCeZIKFy5PZtT5kFla6dWzb1SHfP5/puYl3IxyVzevAwAAAIBYEMqTVKRwebLOhMxIod8h6eV3vQk3o0ztdAAAAADxRChPUrGGxljbJ+uMMrXTAQAAAMQToTxJxRoaP/i8MaayX8k6o0ztdAAAAADxRChPUpHCZXtPbjmoa1bs0JQHN0e18VtPzCjboS643WunAwAAAEguhPIk5KtL/u0Cd8SN3tqriXJH9u6eUS6t9GrKg5t1zYoduv3FPTF9QNDdfLXT3a7ADxTcLifl0AAAAAB0K4cxJrF24opRQ0ODXC6X6uvrlZmZaXV3ouYL1rHW7X7lPa9+9udKHT56wv9YikOKZdLZV1/8rfsuCvuavpJrkgLCv++IaANsqLrgsZ6nu3V2DAAAAAD0brHk0NQ49QkxKK30atmGqoCN1Dwup4pn54cNpw+8UqVngpQo8wXyH0w+TUMG9lPd0RN6quxgyPOcvElb4YjskO18M8rt++qOoq8+keqCO9RWsm16vjvugdhXOx0AAAAAegqh3GZCzRr7lpWHmjV+5b3PggbywHPX6K37LtJf3vssqr78/cN/RZwdLirwaHq+u9MzyrHs4k5ABgAAAJBsuKfcRiLNGktts8btN0BraTX62Z8rI57fF26j3XztyS0Ho7qv2zejPOesr6twRHZMM9rJuos7AAAAAESDUG4jna39XV59WIePNkf1GrWNTTHtzB7txm+dRV1wAAAAAL0ZodxGOjtrHMssck6GM2zZr/bCzdB3B+qCAwAAAOjNCOU20tlZ42iPyx7Q1x9uQ5X9CibUDH13iLUuuB1qmQMAAABAd2GjNxvxzRrX1DcFva/cV6qs/ayx77hwS98l6f/NKQi439u3SdsvNx3Qk1s+jNi/nrqvO9pd3Du7Kz0AAAAA2BWh3EZ8s8YL1+yWQ+oQzI2kq88ZGva4UPPGP5qap29/q2Nw7ZPi0OQzTokqlPfkfd2RdnHv7K70AAAAAGBnDmNMUq//jaVou10EmxE+WajZ4WDHZQ1I0/+fU6CZBZ6Qgbel1WjKg5sjztC/dd9Fca8VLv23f6H+P6zuXzJoaTWdLmsHAAAAIFAsOZRQblMtrUZPbv5Qv3ztQIfnfFEp2OxwsHC1qaom4rJv30y0FDhDH+614mX7wTpds2JHxHa//+Ekapl3ArcFAAAAAN0rlhzKRm829uLOQ0EfD7cjevua4ZuqarRwze4Os8ztS52F2vjN7XJavjScWuY9x/dhTKTfDwAAAAA9g3vKbSqWmuWhZodbWo2WbagKuiTdqG0WfNmGKk3Pd6tPiiPifd1WoZZ5z4j19wMAAABA90uImfKnnnpKeXl5cjqdGj9+vN58802ru9TjumN2OJZg79N+pt0OYYxa5j2jM78fAAAAALqX7UP5H/7wB91xxx1asmSJKioqdP7552vWrFk6dCj40u5k0R2zw8my7DvWWuaITrL8fgAAAACJzPah/LHHHtONN96om266SaNHj9bjjz+uoUOHqqSkxOqu9ajumB1OpmXfdr7nPVEl0+8HAAAAkKhsfU/5iRMn9M4772jx4sUBj8+YMUPbtm0Leszx48d1/Phx//cNDQ092seeEq5mebSzw75gH6nUWaIs+7brPe+JKtl+PwAAAIBEZOuZ8i+++EItLS0aPHhwwOODBw9WTU1N0GMeeOABuVwu/7+hQ4fGo6s9oquzw8m47NuO97wnqmT8/QAAAAASja1nyn0cjsBQYIzp8JjPT37yEy1atMj/fUNDQ8IH867MDvuCffs61G7qUEP8fgAAAABWs3UoP+WUU9SnT58Os+K1tbUdZs990tPTlZ6eHo/uxY1vdrizWPaNcPj9AAAAAKxj61Det29fjR8/Xps2bdLll1/uf3zTpk2aM2eOhT1LPF0N9khu/H4AAAAA1rB1KJekRYsWad68eZowYYIKCwv17LPP6tChQ/rxj39sddcAAAAAAOgS24fyuXPnqq6uTvfff7+8Xq8KCgr0yiuvaPjw4VZ3DQAAAACALnEYY4JVQ0oaDQ0Ncrlcqq+vV2ZmptXdAQAAAAAkuVhyqK1LogEAAAAAkMwI5QAAAAAAWIRQDgAAAACARWy/0RukllZDDWkAAAAASEKEcpsrrfRq2YYqeeub/I95XE4Vz85XUYHHwp4BAAAAALqK5es2Vlrp1cI1uwMCuSTV1Ddp4ZrdKq30WtQzAAAAAEB3IJTbVEur0bINVQpWr8732LINVWppTeqKdgAAAACQ1AjlNlVefbjDDPnJjCRvfZPKqw/Hr1MAAAAAgG5FKLep2sbQgbwz7QAAAAAA9kMot6mcDGe3tgMAAAAA2A+h3KYm5mXJ43IqVOEzh9p2YZ+YlxXPbgEAAAAAuhGh3Kb6pDhUPDtfkjoEc9/3xbPzqVcOAAAAAAmMUG5jRQUelVw/Tm5X4BJ1t8upkuvHUaccAAAAABJcqtUdQHhFBR5Nz3ervPqwahubdMrX0iUjfXH0uLYfrNPEvCxmywEAAAAgQRHKE0CfFIcKR2SrtNKru//4bkCpNI/LqeLZ+cyaAwAAAEACYvl6giit9Grhmt0dapfX1Ddp4ZrdKq30WtQzAAAAAEBnEcoTQEur0bINVTJBnvM9tmxDlVpag7UAAAAAANgVoTwBlFcf7jBDfjIjyVvfpPLqw/HrVA9oaTXafrBOf97zT20/WMeHDAAAAACSHveUJ4DaxtCBvDPt7Ki00qtlG6q4Xx4AAABAr8JMeQLIyXBGbhRDO7vhfnkAAAAAvRWhPAFMzMuSx+VUqMJnDrXNKk/My4pnt7oF98sDAAAA6M0I5QmgT4pDxbPzJalDMPd9Xzw7PyHrlfeW++UBAAAAIBhCeYIoKvCo5PpxcrsCl6i7XU6VXD8uYe+77g33ywMAAABAKGz0lkCKCjyanu9WefVh1TY2KSejbcl6Is6Q+yT7/fIAAAAAEA6hPMH0SXGocES21d3oNr775Wvqm4LeV+5Q22qARLxfHgAAAAAiYfk6LJXM98sDAAAAQCSEclguWe+XBwAAAIBIWL4OW0jG++UBAAAAIBJCOWwj2e6XBwAAAIBIWL4OAAAAAIBFCOUAAAAAAFiEUA4AAAAAgEUI5QAAAAAAWIRQDgAAAACARQjlAAAAAABYhFAOAAAAAIBFCOUAAAAAAFiEUA4AAAAAgEUI5QAAAAAAWIRQDgAAAACARQjlAAAAAABYhFAOAAAAAIBFCOUAAAAAAFiEUA4AAAAAgEVSre4ApJZWo/Lqw6ptbFJOhlMT87LUJ8VhdbcAAAAAAD2MUG6x0kqvlm2okre+yf+Yx+VU8ex8FRV4LOwZAAAAAKCnsXzdQqWVXi1cszsgkEtSTX2TFq7ZrdJKr0U9AwAAAADEA6HcIi2tRss2VMkEec732LINVWppDdYCAAAAAJAMCOUWKa8+3GGG/GRGkre+SeXVh+PXKQAAAABAXBHKLVLbGDqQd6YdAAAAACDxEMotkpPh7NZ2AAAAAIDEQyi3yMS8LHlcToUqfOZQ2y7sE/Oy4tktAAAAAEAcEcot0ifFoeLZ+ZLUIZj7vi+enU+9cgAAAABIYoRyCxUVeFRy/Ti5XYFL1N0up0quH0edcgAAAABIcqlWd6C3KyrwaHq+W+XVh1Xb2KScjLYl68yQAwAAAEDyI5TbQJ8UhwpHZFvdDQAAAABAnLF8HQAAAAAAixDKAQAAAACwCKEcAAAAAACLEMoBAAAAALAIoRwAAAAAAIsQygEAAAAAsAihHAAAAAAAixDKAQAAAACwCKEcAAAAAACLEMoBAAAAALAIoRwAAAAAAIsQygEAAAAAsAihHAAAAAAAixDKAQAAAACwCKEcAAAAAACLEMoBAAAAALAIoRwAAAAAAIsQygEAAAAAsAihHAAAAAAAixDKAQAAAACwSKrVHehpxhhJUkNDg8U9AQAAAAD0Br786cuj4SR9KG9sbJQkDR061OKeAAAAAAB6k8bGRrlcrrBtHCaa6J7AWltb9dlnnykjI0MOh8Pq7gTV0NCgoUOH6tNPP1VmZqbV3UEPYqx7D8a692Csew/GuvdgrHsPxrr3iPdYG2PU2Nio3NxcpaSEv2s86WfKU1JSNGTIEKu7EZXMzEz+GPQSjHXvwVj3Hox178FY9x6Mde/BWPce8RzrSDPkPmz0BgAAAACARQjlAAAAAABYhFBuA+np6SouLlZ6errVXUEPY6x7D8a692Csew/GuvdgrHsPxrr3sPNYJ/1GbwAAAAAA2BUz5QAAAAAAWIRQDgAAAACARQjlAAAAAABYhFAOAAAAAIBFCOU9bOnSpXI4HAH/3G532GO2bt2q8ePHy+l06vTTT9fTTz8dp96iK0477bQOY+1wOHTzzTcHbV9WVha0/T/+8Y849xyRvPHGG5o9e7Zyc3PlcDj00ksvBTxvjNHSpUuVm5urfv366YILLtD7778f8bxr165Vfn6+0tPTlZ+fr/Xr1/fQT4BohRvr5uZm3XfffRozZowGDBig3Nxcff/739dnn30W9pzPP/980Gu9qamph38ahBPpul6wYEGHMZs0aVLE83Jd20+ksQ52fTocDj388MMhz8l1bU8PPPCAzjnnHGVkZCgnJ0eXXXaZ9u/fH9CG9+zkEGmsE+09m1AeB9/85jfl9Xr9//bu3RuybXV1tb797W/r/PPPV0VFhX7605/qtttu09q1a+PYY3TGzp07A8Z506ZNkqTvfe97YY/bv39/wHFnnnlmPLqLGBw9elRjx47Vk08+GfT5hx56SI899piefPJJ7dy5U263W9OnT1djY2PIc27fvl1z587VvHnz9O6772revHm66qqr9Pbbb/fUj4EohBvrY8eOaffu3fr5z3+u3bt3a926dTpw4IAuvfTSiOfNzMwMuM69Xq+cTmdP/AiIUqTrWpKKiooCxuyVV14Je06ua3uKNNbtr83nnntODodDV155Zdjzcl3bz9atW3XzzTdrx44d2rRpk7766ivNmDFDR48e9bfhPTs5RBrrhHvPNuhRxcXFZuzYsVG3v/fee82oUaMCHvvRj35kJk2a1M09Q0+7/fbbzYgRI0xra2vQ57ds2WIkmSNHjsS3Y+gSSWb9+vX+71tbW43b7TbLly/3P9bU1GRcLpd5+umnQ57nqquuMkVFRQGPzZw501x99dXd3md0TvuxDqa8vNxIMp988knINqtWrTIul6t7O4duFWys58+fb+bMmRPTebiu7S+a63rOnDnmoosuCtuG6zox1NbWGklm69atxhjes5NZ+7EOxs7v2cyUx8EHH3yg3Nxc5eXl6eqrr9ZHH30Usu327ds1Y8aMgMdmzpypXbt2qbm5uae7im5y4sQJrVmzRjfccIMcDkfYtmeffbY8Ho8uvvhibdmyJU49RHeprq5WTU1NwHWbnp6uadOmadu2bSGPC3WthzsG9lNfXy+Hw6GBAweGbffvf/9bw4cP15AhQ3TJJZeooqIiPh1El5SVlSknJ0ff+MY39MMf/lC1tbVh23NdJ77PP/9cGzdu1I033hixLde1/dXX10uSsrKyJPGenczaj3WoNnZ9zyaU97Bzzz1Xv/3tb/Xqq69qxYoVqqmp0Xnnnae6urqg7WtqajR48OCAxwYPHqyvvvpKX3zxRTy6jG7w0ksv6csvv9SCBQtCtvF4PHr22We1du1arVu3TiNHjtTFF1+sN954I34dRZfV1NRIUtDr1vdcqONiPQb20tTUpMWLF+vaa69VZmZmyHajRo3S888/r5dfflm///3v5XQ6NXnyZH3wwQdx7C1iNWvWLP3ud7/T5s2b9eijj2rnzp266KKLdPz48ZDHcF0nvtWrVysjI0NXXHFF2HZc1/ZnjNGiRYs0ZcoUFRQUSOI9O1kFG+v27P6endqjZ4dmzZrl/3rMmDEqLCzUiBEjtHr1ai1atCjoMe1nVo0xQR+Hfa1cuVKzZs1Sbm5uyDYjR47UyJEj/d8XFhbq008/1SOPPKKpU6fGo5voRsGu20jXbGeOgT00Nzfr6quvVmtrq5566qmwbSdNmhSwQdjkyZM1btw4/frXv9YTTzzR011FJ82dO9f/dUFBgSZMmKDhw4dr48aNYQMb13Vie+6553TddddFvH+U69r+brnlFr333nt66623OjzHe3ZyCTfWUmK8ZzNTHmcDBgzQmDFjQn7a4na7O3zqVltbq9TUVGVnZ8eji+iiTz75RK+99ppuuummmI+dNGkSn7InGF81hWDXbftP1dsfF+sxsIfm5mZdddVVqq6u1qZNm8J+4h5MSkqKzjnnHK71BOPxeDR8+PCw48Z1ndjefPNN7d+/v1Pv31zX9nLrrbfq5Zdf1pYtWzRkyBD/47xnJ59QY+2TKO/ZhPI4O378uPbt2yePxxP0+cLCQv+u3T5/+9vfNGHCBKWlpcWji+iiVatWKScnR9/5zndiPraioiLk7wbsKS8vT263O+C6PXHihLZu3arzzjsv5HGhrvVwx8B6vjf3Dz74QK+99lqnPiw1xmjPnj1c6wmmrq5On376adhx47pObCtXrtT48eM1duzYmI/lurYHY4xuueUWrVu3Tps3b1ZeXl7A87xnJ49IYy0l2Ht23LeW62XuuusuU1ZWZj766COzY8cOc8kll5iMjAzz8ccfG2OMWbx4sZk3b56//UcffWT69+9v7rzzTlNVVWVWrlxp0tLSzJ/+9CerfgTEoKWlxQwbNszcd999HZ5rP9a//OUvzfr1682BAwdMZWWlWbx4sZFk1q5dG88uIwqNjY2moqLCVFRUGEnmscceMxUVFf7dO5cvX25cLpdZt26d2bt3r7nmmmuMx+MxDQ0N/nPMmzfPLF682P/93//+d9OnTx+zfPlys2/fPrN8+XKTmppqduzYEfefD/8Vbqybm5vNpZdeaoYMGWL27NljvF6v/9/x48f952g/1kuXLjWlpaXm4MGDpqKiwvzgBz8wqamp5u2337biR8R/hBvrxsZGc9ddd5lt27aZ6upqs2XLFlNYWGi+/vWvc10noEh/w40xpr6+3vTv39+UlJQEPQfXdWJYuHChcblcpqysLOBv9LFjx/xteM9ODpHGOtHeswnlPWzu3LnG4/GYtLQ0k5uba6644grz/vvv+5+fP3++mTZtWsAxZWVl5uyzzzZ9+/Y1p512Wsg3CNjPq6++aiSZ/fv3d3iu/Vg/+OCDZsSIEcbpdJpBgwaZKVOmmI0bN8axt4iWr3xd+3/z5883xrSVWCkuLjZut9ukp6ebqVOnmr179wacY9q0af72Pn/84x/NyJEjTVpamhk1ahQfyNhAuLGurq4O+pwks2XLFv852o/1HXfcYYYNG2b69u1rTj31VDNjxgyzbdu2+P9wCBBurI8dO2ZmzJhhTj31VJOWlmaGDRtm5s+fbw4dOhRwDq7rxBDpb7gxxjzzzDOmX79+5ssvvwx6Dq7rxBDqb/SqVav8bXjPTg6RxjrR3rMd//mhAAAAAABAnHFPOQAAAAAAFiGUAwAAAABgEUI5AAAAAAAWIZQDAAAAAGARQjkAAAAAABYhlAMAAAAAYBFCOQAAAAAAFiGUAwAAAABgEUI5AADoYMGCBbrssstscx4AAJIVoRwAAJtZsGCBHA6HHA6H0tLSdPrpp+vuu+/W0aNHre5aSB9//LEcDof27NkT8PivfvUrPf/885b0CQCARJBqdQcAAEBHRUVFWrVqlZqbm/Xmm2/qpptu0tGjR1VSUmJ112Licrms7gIAALbGTDkAADaUnp4ut9utoUOH6tprr9V1112nl156ScePH9dtt92mnJwcOZ1OTZkyRTt37vQfV1ZWJofDoY0bN2rs2LFyOp0699xztXfvXn+bpUuX6qyzzgp4vccff1ynnXZayP6UlpZqypQpGjhwoLKzs3XJJZfo4MGD/ufz8vIkSWeffbYcDocuuOACSR2Xr0fb/9dff10TJkxQ//79dd5552n//v2d+F8EAMD+COUAACSAfv36qbm5Wffee6/Wrl2r1atXa/fu3TrjjDM0c+ZMHT58OKD9Pffco0ceeUQ7d+5UTk6OLr30UjU3N3f69Y8ePapFixZp586dev3115WSkqLLL79cra2tkqTy8nJJ0muvvSav16t169YFPU+0/V+yZIkeffRR7dq1S6mpqbrhhhs63XcAAOyMUA4AgM2Vl5frhRde0IUXXqiSkhI9/PDDmjVrlvLz87VixQr169dPK1euDDimuLhY06dP15gxY7R69Wp9/vnnWr9+faf7cOWVV+qKK67QmWeeqbPOOksrV67U3r17VVVVJUk69dRTJUnZ2dlyu93KysrqcA7f8vto+v+LX/xC06ZNU35+vhYvXqxt27apqamp0/0HAMCuCOUAANjQX/7yF33ta1+T0+lUYWGhpk6dqltvvVXNzc2aPHmyv11aWpomTpyoffv2BRxfWFjo/zorK0sjR47s0CYWBw8e1LXXXqvTTz9dmZmZ/uXqhw4diukc0fb/W9/6lv9rj8cjSaqtre10/wEAsCs2egMAwIZ8s+JpaWnKzc1VWlqa3n33XUmSw+EIaGuM6fBYML42KSkpMsYEPBdpafvs2bM1dOhQrVixQrm5uWptbVVBQYFOnDgR9c/ke81o+p+Wltah376l8gAAJBNmygEAsKEBAwbojDPO0PDhw/0B9YwzzlDfvn311ltv+ds1Nzdr165dGj16dMDxO3bs8H995MgRHThwQKNGjZLUttS8pqYmIJi3L2V2srq6Ou3bt08/+9nPdPHFF2v06NE6cuRIQJu+fftKklpaWkKeJ5b+AwDQWzBTDgBAghgwYIAWLlyoe+65R1lZWRo2bJgeeughHTt2TDfeeGNA2/vvv1/Z2dkaPHiwlixZolNOOcW/C/oFF1ygf/3rX3rooYf03e9+V6WlpfrrX/+qzMzMoK87aNAgZWdn69lnn5XH49GhQ4e0ePHigDY5OTnq16+fSktLNWTIEDmdzg7l0GLpPwAAvQUz5QAAJJDly5fryiuv1Lx58zRu3Dh9+OGHevXVVzVo0KAO7W6//XaNHz9eXq9XL7/8sn82e/To0Xrqqaf0m9/8RmPHjlV5ebnuvvvukK+ZkpKiF198Ue+8844KCgp055136uGHHw5ok5qaqieeeELPPPOMcnNzNWfOnC71HwCA3sJh2t9UBgAAElZZWZkuvPBCHTlyRAMHDrS6OwAAIAJmygEAAAAAsAihHAAAAAAAi7B8HQAAAAAAizBTDgAAAACARQjlAAAAAABYhFAOAAAAAIBFCOUAAAAAAFiEUA4AAAAAgEUI5QAAAAAAWIRQDgAAAACARQjlAAAAAABY5P8A9kLA4f5jeuAAAAAASUVORK5CYII=", "text/plain": ["<Figure size 1200x800 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["x = np.linspace(data.Population.min(), data.Population.max(), 100)\n", "f = g[0, 0] + (g[0, 1] * x)\n", "\n", "fig, ax = plt.subplots(figsize=(12,8))\n", "ax.plot(x, f, 'r', label='Prediction')\n", "ax.scatter(data.Population, data.Profit, label='Traning Data')\n", "ax.legend(loc=2)\n", "ax.set_xlabel('Population')\n", "ax.set_ylabel('Profit')\n", "ax.set_title('Predicted Profit vs. Population Size')\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["由于梯度方程式函数也在每个训练迭代中输出一个代价的向量，所以我们也可以绘制。 请注意，代价总是降低 - 这是凸优化问题的一个例子。"]}, {"cell_type": "code", "execution_count": 44, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1200x800 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["fig, ax = plt.subplots(figsize=(12,8))\n", "ax.plot(np.arange(iters), cost, 'r')\n", "ax.set_xlabel('Iterations')\n", "ax.set_ylabel('Cost')\n", "ax.set_title('Error vs. Training Epoch')\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 多变量线性回归"]}, {"cell_type": "markdown", "metadata": {}, "source": ["练习1还包括一个房屋价格数据集，其中有2个变量（房子的大小，卧室的数量）和目标（房子的价格）。 我们使用我们已经应用的技术来分析数据集。"]}, {"cell_type": "code", "execution_count": 45, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Size</th>\n", "      <th>Bedrooms</th>\n", "      <th>Price</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2104</td>\n", "      <td>3</td>\n", "      <td>399900</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>1600</td>\n", "      <td>3</td>\n", "      <td>329900</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2400</td>\n", "      <td>3</td>\n", "      <td>369000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>1416</td>\n", "      <td>2</td>\n", "      <td>232000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>3000</td>\n", "      <td>4</td>\n", "      <td>539900</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   Size  Bedrooms   Price\n", "0  2104         3  399900\n", "1  1600         3  329900\n", "2  2400         3  369000\n", "3  1416         2  232000\n", "4  3000         4  539900"]}, "execution_count": 45, "metadata": {}, "output_type": "execute_result"}], "source": ["path =  'ex1data2.txt'\n", "data2 = pd.read_csv(path, header=None, names=['Size', 'Bedrooms', 'Price'])\n", "data2.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["对于此任务，我们添加了另一个预处理步骤 - 特征归一化。 这个对于pandas来说很简单"]}, {"cell_type": "code", "execution_count": 46, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Size</th>\n", "      <th>Bedrooms</th>\n", "      <th>Price</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>0.130010</td>\n", "      <td>-0.223675</td>\n", "      <td>0.475747</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>-0.504190</td>\n", "      <td>-0.223675</td>\n", "      <td>-0.084074</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>0.502476</td>\n", "      <td>-0.223675</td>\n", "      <td>0.228626</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>-0.735723</td>\n", "      <td>-1.537767</td>\n", "      <td>-0.867025</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>1.257476</td>\n", "      <td>1.090417</td>\n", "      <td>1.595389</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["       Size  Bedrooms     Price\n", "0  0.130010 -0.223675  0.475747\n", "1 -0.504190 -0.223675 -0.084074\n", "2  0.502476 -0.223675  0.228626\n", "3 -0.735723 -1.537767 -0.867025\n", "4  1.257476  1.090417  1.595389"]}, "execution_count": 46, "metadata": {}, "output_type": "execute_result"}], "source": ["data2 = (data2 - data2.mean()) / data2.std()\n", "data2.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["现在我们重复第1部分的预处理步骤，并对新数据集运行线性回归程序。"]}, {"cell_type": "code", "execution_count": 47, "metadata": {}, "outputs": [{"data": {"text/plain": ["0.13070336960771892"]}, "execution_count": 47, "metadata": {}, "output_type": "execute_result"}], "source": ["# add ones column\n", "data2.insert(0, 'Ones', 1)\n", "\n", "# set X (training data) and y (target variable)\n", "cols = data2.shape[1]\n", "X2 = data2.iloc[:,0:cols-1]\n", "y2 = data2.iloc[:,cols-1:cols]\n", "\n", "# convert to matrices and initialize theta\n", "X2 = np.matrix(X2.values)\n", "y2 = np.matrix(y2.values)\n", "theta2 = np.matrix(np.array([0,0,0]))\n", "\n", "# perform linear regression on the data set\n", "g2, cost2 = gradientDescent(X2, y2, theta2, alpha, iters)\n", "\n", "# get the cost (error) of the model\n", "computeCost(X2, y2, g2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["我们也可以快速查看这一个的训练进程。"]}, {"cell_type": "code", "execution_count": 48, "metadata": {"scrolled": true}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1200x800 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["fig, ax = plt.subplots(figsize=(12,8))\n", "ax.plot(np.arange(iters), cost2, 'r')\n", "ax.set_xlabel('Iterations')\n", "ax.set_ylabel('Cost')\n", "ax.set_title('Error vs. Training Epoch')\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["我们也可以使用scikit-learn的线性回归函数，而不是从头开始实现这些算法。 我们将scikit-learn的线性回归算法应用于第1部分的数据，并看看它的表现。"]}, {"cell_type": "code", "execution_count": 51, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Scikit-learn model coefficients: theta1 = 1.1930, theta0 = -3.8958\n"]}], "source": ["from sklearn import linear_model\n", "import numpy as np\n", "\n", "# scikit-learn需要普通的numpy数组，不支持np.matrix\n", "# 将matrix转换为普通数组\n", "X_array = np.asarray(X)\n", "y_array = np.asarray(y)\n", "\n", "model = linear_model.LinearRegression()\n", "model.fit(X_array, y_array.ravel())  # 使用.ravel()将二维数组展平为一维\n", "\n", "# 打印学习到的参数，以便查看结果\n", "# model.coef_ 是斜率, model.intercept_ 是截距\n", "print(f\"Scikit-learn model coefficients: theta1 = {model.coef_[1]:.4f}, theta0 = {model.intercept_:.4f}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["scikit-learn model的预测表现"]}, {"cell_type": "code", "execution_count": 52, "metadata": {"scrolled": true}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1200x800 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["x = np.array(X[:, 1].A1)\n", "# 使用转换后的 X_array 进行预测\n", "f = model.predict(X_array).flatten()\n", "\n", "fig, ax = plt.subplots(figsize=(12,8))\n", "ax.plot(x, f, 'r', label='Prediction')\n", "ax.scatter(data.Population, data.Profit, label='Traning Data')\n", "ax.legend(loc=2)\n", "ax.set_xlabel('Population')\n", "ax.set_ylabel('Profit')\n", "ax.set_title('Predicted Profit vs. Population Size')\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 4. normal equation（正规方程）\n", "正规方程是通过求解下面的方程来找出使得代价函数最小的参数的：$\\frac{\\partial }{\\partial {{\\theta }_{j}}}J\\left( {{\\theta }_{j}} \\right)=0$ 。\n", " 假设我们的训练集特征矩阵为 X（包含了${{x}_{0}}=1$）并且我们的训练集结果为向量 y，则利用正规方程解出向量 $\\theta ={{\\left( {{X}^{T}}X \\right)}^{-1}}{{X}^{T}}y$ 。\n", "上标T代表矩阵转置，上标-1 代表矩阵的逆。设矩阵$A={{X}^{T}}X$，则：${{\\left( {{X}^{T}}X \\right)}^{-1}}={{A}^{-1}}$\n", "\n", "梯度下降与正规方程的比较：\n", "\n", "梯度下降：需要选择学习率α，需要多次迭代，当特征数量n大时也能较好适用，适用于各种类型的模型\t\n", "\n", "正规方程：不需要选择学习率α，一次计算得出，需要计算${{\\left( {{X}^{T}}X \\right)}^{-1}}$，如果特征数量n较大则运算代价大，因为矩阵逆的计算时间复杂度为$O(n3)$，通常来说当$n$小于10000 时还是可以接受的，只适用于线性模型，不适合逻辑回归模型等其他模型"]}, {"cell_type": "code", "execution_count": 53, "metadata": {"collapsed": true}, "outputs": [], "source": ["# 正规方程\n", "def normalEqn(X, y):\n", "    theta = np.linalg.inv(X.T@X)@X.T@y#X.T@X等价于X.T.dot(X)\n", "    return theta"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/plain": ["matrix([[-3.89578088],\n", "        [ 1.19303364]])"]}, "execution_count": 54, "metadata": {}, "output_type": "execute_result"}], "source": ["final_theta2=normalEqn(X, y)#感觉和批量梯度下降的theta的值有点差距\n", "final_theta2"]}, {"cell_type": "code", "execution_count": 55, "metadata": {"collapsed": true}, "outputs": [], "source": ["#梯度下降得到的结果是matrix([[-3.24140214,  1.1272942 ]])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["在练习2中，我们将看看分类问题的逻辑回归。"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.9"}}, "nbformat": 4, "nbformat_minor": 1}